{#
/**
 * @file
 * Theme implementation for a 'webform' element.
 *
 * This is an copy of the webform.html.twig theme_wrapper which includes the
 * 'title_prefix' and 'title_suffix' variables needed for
 * contextual links to appear.
 *
 * Available variables
 * - attributes: A list of HTML attributes for the wrapper element.
 * - children: The child elements of the webform.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the main title tag that appears in the template.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 *
 * @see template_preprocess_webform()
 * @see _webform_form_after_build()
 *
 * @ingroup themeable
 */
#}



        <div class="container-fluid">
           <div class="row">
<div class="col fondbleu2 pt-5">
    <h1 class="fs-1 text-start text-light fw-bold pt-3">
        <svg class="bi bi-square-fill" xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#f29432" viewBox="0 0 16 16"><path d="M0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2z"></path></svg>&nbsp;Contact us
    </h1>
    
        <p class="text-light fs-6 text-start mx-3 p-4 pt-1">
        Please do not hesitate to get in touch with us<br>
        using this contact form. We will be happy<br>
        to help you with any questions<br>
        related to the use of this platform,<br>
        CreaFREE services, or intellectual property.
      </p>
      <p class="text-light fs-6 text-start mx-3 p-4 pt-1 mt-4">
        We also welcome any requests for specific support<br>
        or suggestions for improvement.
      </p>
       
    
</div>


 <div class="col-md-8 col-12">

  <!-- First row with 3 columns -->
  <div class="row p-5">
<form{{ attributes }}>
  {{ title_prefix }}
  {{ children }}
  {{ title_suffix }}
</form>
</div>
</div>
</div>