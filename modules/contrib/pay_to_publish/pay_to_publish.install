<?php

use Dr<PERSON>al\field\Entity\FieldConfig;
use Drupal\field\Entity\FieldStorageConfig;

/**
 * Implements hook_install().
 */
function pay_to_publish_install() {
  // Create the field storage configuration for 'field_node_references1' on nodes.
  $field_storage = \Drupal::entityTypeManager()
    ->getStorage('field_storage_config')
    ->load('node.field_node_references1');

  if (!$field_storage) {
    $field_storage = FieldStorageConfig::create([
      'field_name' => 'field_node_references1',
      'entity_type' => 'node',
      'type' => 'entity_reference',
      'settings' => [
        'target_type' => 'commerce_order_item',
      ],
      'cardinality' => 1,
      'translatable' => FALSE,
    ]);
    $field_storage->save();
  }

  // Create the field configuration for the 'creabook' content type.
  $field_config = \Drupal::entityTypeManager()
    ->getStorage('field_config')
    ->load('node.creabook.field_node_references1');

  if (!$field_config) {
    $field_config = FieldConfig::create([
      'field_storage' => $field_storage,
      'bundle' => 'creabook',
      'label' => t('Order Reference'),
      'description' => t('Reference to the commerce order item.'),
      'required' => FALSE,
    ]);
    $field_config->save();
  }
}

/**
 * Implements hook_uninstall().
 */
function pay_to_publish_uninstall() {
  // Remove the field configuration for the 'creabook' content type.
  $field_config = \Drupal::entityTypeManager()
    ->getStorage('field_config')
    ->load('node.creabook.field_node_references1');

  if ($field_config) {
    $field_config->delete();
  }

  // Remove the field storage configuration for 'field_node_references1' on nodes.
  $field_storage = \Drupal::entityTypeManager()
    ->getStorage('field_storage_config')
    ->load('node.field_node_references1');

  if ($field_storage) {
    $field_storage->delete();
  }
}
