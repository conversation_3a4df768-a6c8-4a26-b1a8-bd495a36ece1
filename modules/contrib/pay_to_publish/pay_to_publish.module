<?php

use Drupal\Core\Link;
use Drupal\Core\Url;
use <PERSON>upal\node\Entity\Node;
use Drupal\Core\Entity\Display\EntityViewDisplayInterface;
use Drupal\Core\Entity\EntityInterface;
use Drupal\node\NodeInterface;
use Drupal\commerce_order\Entity\Order;
use Drupal\commerce_order\Entity\OrderInterface;
use Drupal\commerce_order\Entity\OrderItem;
use Drupal\commerce_product\Entity\ProductVariation;
use Drupal\commerce_store\Entity\StoreInterface;


/**
 * Implements hook_node_view_alter() to add a Pay to Publish button.
 */
function pay_to_publish_node_view_alter(array &$build, EntityInterface $entity, EntityViewDisplayInterface $display) {
  // Check if the entity is a node of type 'creabook'.
  if ($entity instanceof NodeInterface && $entity->bundle() === 'creabook' && !$entity->isPublished()) {
    $current_user_id = \Drupal::currentUser()->id();
    $node_owner_id = $entity->getOwnerId();

    // Only add the button for unpublished creabook nodes created by the current user.
    if ($current_user_id == $node_owner_id) {
      $url = Url::fromRoute('pay_to_publish.payment_form', ['node' => $entity->id()]);
      $link = Link::fromTextAndUrl(t('Pay to Publish'), $url)->toRenderable();
      $link['#attributes'] = ['class' => ['button', 'button--primary']];
      $build['pay_to_publish'] = [
        '#type' => 'container',
        '#attributes' => ['class' => ['pay-to-publish']],
        'link' => $link,
        '#attached' => [
          'library' => [
            'pay_to_publish/timer',
          ],
        ],
      ];
    }
  }
}
    

/**
 * Implements hook_preprocess_HOOK() for node templates.
 */
function pay_to_publish_preprocess_node(array &$variables) {
  if (isset($variables['node']) && $variables['node'] instanceof NodeInterface) {
    $node = $variables['node'];
    // Only show the button for unpublished 'creabook' nodes owned by the current user.
    if ($node->getType() === 'creabook' && !$node->isPublished()) {
      $current_user = \Drupal::currentUser();
      \Drupal::logger('pay_to_publish')->debug('Checking ownership and status.');

      if ($node->getOwnerId() === $current_user->id()) {
        \Drupal::logger('pay_to_publish')->debug('User is owner, adding button.');

        // Create a URL for the "Pay to Publish" action.
        $url = Url::fromRoute('pay_to_publish.payment_form', ['node' => $node->id()]);

        // Build a renderable link for the button.
        $variables['pay_to_publish_button'] = [
          '#type' => 'link',
          '#title' => t('Pay to Publish'),
          '#url' => $url,
          '#attributes' => [
            'class' => ['btn', 'btn-primary'],
          ],
        ];
      } else {
        \Drupal::logger('pay_to_publish')->debug('User is not the owner.');
      }
    } else {
      \Drupal::logger('pay_to_publish')->debug('Node is not unpublished or not a creabook.');
    }
  }
}

/**
 * Implements hook_entity_insert().
 *
 * This hook is triggered when a node is created and first saved.
 *
 * @param \Drupal\Core\Entity\EntityInterface $entity
 *   The entity being created.
 */
function pay_to_publish_entity_insert(EntityInterface $entity) {
  // Check if the entity is a node of type 'creabook'.
  if ($entity instanceof NodeInterface && $entity->bundle() === 'creabook') {
    // Retrieve the order ID from the user's session.
    $order_id = getOrderIdFromSession();

    // Load the order entity using the order ID.
    if ($order_id) {
      $order = Order::load($order_id);
      if ($order) {
        // Set the order reference field on the node.
        setOrderReference($entity, $order);

        // Optionally, clear the order ID from the session after using it.
        clearOrderIdFromSession();
      }
    }
  }
}

/**
 * Implements hook_commerce_order_update().
 *
 * This hook is triggered whenever an order is updated.
 *
 * @param \Drupal\commerce_order\Entity\OrderInterface $order
 *   The order being updated.
 */
function pay_to_publish_commerce_order_update(OrderInterface $order) {
  // Check if the order state is 'completed'.
      if ($order->hasField('state') && $order->get('state')->value === 'completed') {
    // Iterate through order items.
    foreach ($order->getItems() as $order_item) {
      // Access the node reference field.
      if ($order_item->hasField('field_node_references1')) {
        $node_reference = $order_item->get('field_node_references1')->entity;
        if ($node_reference instanceof Node && !$node_reference->isPublished()) {
          // Publish the node.
          $node_reference->setPublished(TRUE);
          $node_reference->save();

          // Log the publication action.
          \Drupal::logger('pay_to_publish')->notice('Node @nid has been published after order completion.', ['@nid' => $node_reference->id()]);

          // Optional: Display a message to the user (or administrator).
          \Drupal::messenger()->addStatus(t('The node @title has been published.', ['@title' => $node_reference->getTitle()]));
        }
      }
    }
  }
}


/**
 * Retrieves the order ID from the user's session.
 *
 * @return int|null
 *   The order ID if available, NULL otherwise.
 */
function getOrderIdFromSession() {
  $session = \Drupal::service('session');
  return $session->get('current_order_id');
}

/**
 * Clears the order ID from the user's session.
 */
function clearOrderIdFromSession() {
  $session = \Drupal::service('session');
  $session->remove('current_order_id');
}

/**
 * Set order reference for a node.
 *
 * @param \Drupal\node\Entity\Node $node
 *   The node to update.
 * @param \Drupal\commerce_order\Entity\Order $order
 *   The order to reference.
 */
function setOrderReference(Node $node, Order $order) {
  // Ensure the field exists on the node.
  if ($node->hasField('field_node_references1')) {
    $node->set('field_node_references1', $order->id());
    $node->save();
  }
}

function pay_to_publish_mail($key, &$message, $params) {
  switch ($key) {
    case 'node_published_notification':
      $message['subject'] = t('Your Creabook has been published');
      $message['body'][] = t('Hello @name,', ['@name' => $params['username']]);
      $message['body'][] = t('Your Creabook titled "@title" has been successfully published.', ['@title' => $params['title']]);
      $message['body'][] = t('Thank you for using our service.');
      break;
  }
}

