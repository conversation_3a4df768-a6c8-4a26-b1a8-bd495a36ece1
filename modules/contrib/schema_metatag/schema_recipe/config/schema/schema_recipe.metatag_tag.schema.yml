# The 'type' should be "label" for short meta tags and "text" for ones which
# could get longer, especially ones which use a textarea field instead of a
# textfield.
metatag.metatag_tag.schema_recipe_author:
  type: text
  label: 'author'
metatag.metatag_tag.schema_recipe_cook_time:
  type: text
  label: 'cookTime'
metatag.metatag_tag.schema_recipe_date_published:
  type: text
  label: 'datePublished'
metatag.metatag_tag.schema_recipe_description:
  type: text
  label: 'description'
metatag.metatag_tag.schema_recipe_id:
  type: text
  label: '@id'
metatag.metatag_tag.schema_recipe_image:
  type: text
  label: 'image'
metatag.metatag_tag.schema_recipe_name:
  type: text
  label: 'name'
metatag.metatag_tag.schema_recipe_prep_time:
  type: text
  label: 'prepTime'
metatag.metatag_tag.schema_recipe_recipe_category:
  type: text
  label: 'recipeCategory'
metatag.metatag_tag.schema_recipe_recipe_ingredient:
  type: text
  label: 'recipeIngredient'
metatag.metatag_tag.schema_recipe_recipe_instructions:
  type: text
  label: 'recipeInstructions'
metatag.metatag_tag.schema_recipe_recipe_yield:
  type: text
  label: 'recipeYield'
metatag.metatag_tag.schema_recipe_total_time:
  type: text
  label: 'totalTime'
metatag.metatag_tag.schema_recipe_type:
  type: string
  label: '@type'
metatag.metatag_tag.schema_recipe_aggregate_rating:
  type: text
  label: 'aggregateRating'
metatag.metatag_tag.schema_recipe_review:
  type: text
  label: 'review'
metatag.metatag_tag.schema_recipe_nutrition:
  type: text
  label: 'nutrition'
metatag.metatag_tag.schema_recipe_recipe_cuisine:
  type: text
  label: 'recipeCuisine'
metatag.metatag_tag.schema_recipe_keywords:
  type: text
  label: 'keywords'
