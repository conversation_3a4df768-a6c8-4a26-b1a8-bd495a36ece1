<?php

namespace Drupal\schema_organization\Plugin\metatag\Tag;

use Drupal\schema_metatag\Plugin\metatag\Tag\SchemaNameBase;

/**
 * Provides a plugin for the 'schema_organization_menu' meta tag.
 *
 * - 'id' should be a globally unique id.
 * - 'name' should match the Schema.org element name.
 * - 'group' should match the id of the group that defines the Schema.org type.
 *
 * @MetatagTag(
 *   id = "schema_organization_menu",
 *   label = @Translation("menu"),
 *   description = @Translation("RECOMMENDED BY GOOGLE for food establishments, the fully-qualified URL of the menu."),
 *   name = "menu",
 *   group = "schema_organization",
 *   weight = 5,
 *   type = "string",
 *   secure = FALSE,
 *   multiple = FALSE,
 *   property_type = "url",
 *   tree_parent = {},
 *   tree_depth = -1,
 * )
 */
class SchemaOrganizationMenu extends SchemaNameBase {

}
