# The 'type' should be "label" for short meta tags and "text" for ones which
# could get longer, especially ones which use a textarea field instead of a
# textfield.
metatag.metatag_tag.schema_product_description:
  type: text
  label: 'description'
metatag.metatag_tag.schema_product_id:
  type: text
  label: '@id'
metatag.metatag_tag.schema_product_image:
  type: text
  label: 'image'
metatag.metatag_tag.schema_product_name:
  type: text
  label: 'name'
metatag.metatag_tag.schema_product_url:
  type: text
  label: 'url'
metatag.metatag_tag.schema_product_category:
  type: text
  label: 'category'
metatag.metatag_tag.schema_product_offers:
  type: text
  label: 'offers'
metatag.metatag_tag.schema_product_type:
  type: string
  label: '@type'
metatag.metatag_tag.schema_product_aggregate_rating:
  type: text
  label: 'aggregateRating'
metatag.metatag_tag.schema_product_review:
  type: text
  label: 'review'
metatag.metatag_tag.schema_product_brand:
  type: text
  label: 'brand'
metatag.metatag_tag.schema_product_sku:
  type: text
  label: 'sku'
metatag.metatag_tag.schema_product_gtin8:
  type: text
  label: 'gtin8'
metatag.metatag_tag.schema_product_gtin12:
  type: text
  label: 'gtin12'
metatag.metatag_tag.schema_product_gtin13:
  type: text
  label: 'gtin13'
metatag.metatag_tag.schema_product_gtin14:
  type: text
  label: 'gtin14'
metatag.metatag_tag.schema_product_isbn:
  type: text
  label: 'isbn'
metatag.metatag_tag.schema_product_mpn:
  type: text
  label: 'mpn'
