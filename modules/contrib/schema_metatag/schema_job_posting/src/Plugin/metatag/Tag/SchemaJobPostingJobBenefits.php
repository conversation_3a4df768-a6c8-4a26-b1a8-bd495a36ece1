<?php

namespace Drupal\schema_job_posting\Plugin\metatag\Tag;

use <PERSON><PERSON>al\schema_metatag\Plugin\metatag\Tag\SchemaNameBase;

/**
 * Provides a plugin for the 'schema_job_posting_job_benefits' meta tag.
 *
 * - 'id' should be a globally unique id.
 * - 'name' should match the Schema.org element name.
 * - 'group' should match the id of the group that defines the Schema.org type.
 *
 * @MetatagTag(
 *   id = "schema_job_posting_job_benefits",
 *   label = @Translation("jobBenefits"),
 *   description = @Translation("The benefits of the position."),
 *   name = "jobBenefits",
 *   group = "schema_job_posting",
 *   weight = 15,
 *   type = "string",
 *   secure = FALSE,
 *   multiple = FALSE,
 *   property_type = "text",
 *   tree_parent = {},
 *   tree_depth = -1,
 * )
 */
class SchemaJobPostingJobBenefits extends SchemaNameBase {

}
