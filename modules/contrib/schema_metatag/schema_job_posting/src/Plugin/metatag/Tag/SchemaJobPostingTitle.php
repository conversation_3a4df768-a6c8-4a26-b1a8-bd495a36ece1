<?php

namespace Drupal\schema_job_posting\Plugin\metatag\Tag;

use <PERSON>upal\schema_metatag\Plugin\metatag\Tag\SchemaNameBase;

/**
 * Provides a plugin for the 'schema_job_posting_title' meta tag.
 *
 * - 'id' should be a globally unique id.
 * - 'name' should match the Schema.org element name.
 * - 'group' should match the id of the group that defines the Schema.org type.
 *
 * @MetatagTag(
 *   id = "schema_job_posting_title",
 *   label = @Translation("title"),
 *   description = @Translation("REQUIRED BY GOOGLE. The title of the job."),
 *   name = "title",
 *   group = "schema_job_posting",
 *   weight = -7,
 *   type = "string",
 *   secure = FALSE,
 *   multiple = FALSE,
 *   property_type = "text",
 *   tree_parent = {},
 *   tree_depth = -1,
 * )
 */
class SchemaJobPostingTitle extends SchemaNameBase {

}
