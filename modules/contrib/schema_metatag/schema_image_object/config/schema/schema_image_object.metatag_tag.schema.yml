# The 'type' should be "label" for short meta tags and "text" for ones which
# could get longer, especially ones which use a textarea field instead of a
# textfield.
metatag.metatag_tag.schema_image_object_content_url:
  type: text
  label: 'url'
metatag.metatag_tag.schema_image_object_description:
  type: text
  label: 'description'
metatag.metatag_tag.schema_image_object_height:
  type: text
  label: 'height'
metatag.metatag_tag.schema_image_object_id:
  type: text
  label: '@id'
metatag.metatag_tag.schema_image_object_name:
  type: text
  label: 'name'
metatag.metatag_tag.schema_image_object_type:
  type: string
  label: '@type'
metatag.metatag_tag.schema_image_object_url:
  type: text
  label: 'url'
metatag.metatag_tag.schema_image_object_width:
  type: text
  label: 'width'
metatag.metatag_tag.schema_image_object_aggregate_rating:
  type: text
  label: 'aggregateRating'
metatag.metatag_tag.schema_image_object_review:
  type: text
  label: 'review'
