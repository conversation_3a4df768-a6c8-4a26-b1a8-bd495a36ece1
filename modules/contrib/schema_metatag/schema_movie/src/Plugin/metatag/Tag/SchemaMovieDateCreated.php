<?php

namespace Drupal\schema_movie\Plugin\metatag\Tag;

use Drupal\schema_metatag\Plugin\metatag\Tag\SchemaNameBase;

/**
 * Provides a plugin for the 'dateCreated' meta tag.
 *
 * - 'id' should be a globally unique id.
 * - 'name' should match the Schema.org element name.
 * - 'group' should match the id of the group that defines the Schema.org type.
 *
 * @MetatagTag(
 *   id = "schema_movie_date_created",
 *   label = @Translation("dateCreated"),
 *   description = @Translation("RECOMMENDED BY GOOGLE. The date on which the work was created or was added to a DataFeed."),
 *   name = "dateCreated",
 *   group = "schema_movie",
 *   weight = 3,
 *   type = "string",
 *   secure = FALSE,
 *   multiple = FALSE,
 *   property_type = "date",
 *   tree_parent = {},
 *   tree_depth = -1,
 * )
 */
class SchemaMovieDateCreated extends SchemaNameBase {

}
