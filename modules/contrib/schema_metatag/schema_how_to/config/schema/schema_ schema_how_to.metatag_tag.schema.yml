# The 'type' should be "label" for short meta tags and "text" for ones which
# could get longer, especially ones which use a textarea field instead of a
# textfield.
metatag.metatag_tag.schema_how_to_type:
  type: string
  label: '@type'
metatag.metatag_tag.schema_how_to_name:
  type: text
  label: 'name'
metatag.metatag_tag.schema_how_to_step:
  type: text
  label: 'step'
metatag.metatag_tag.schema_how_to_description:
  type: text
  label: 'description'
metatag.metatag_tag.schema_how_to_id:
  type: text
  label: '@id'
metatag.metatag_tag.schema_how_to_image:
  type: text
  label: 'image'
metatag.metatag_tag.schema_how_to_estimated_cost:
  type: text
  label: 'estimatedCost'
metatag.metatag_tag.schema_how_to_supply:
  type: text
  label: 'supply'
metatag.metatag_tag.schema_how_to_tool:
  type: text
  label: 'tool'
metatag.metatag_tag.schema_how_to_url:
  type: text
  label: 'url'
