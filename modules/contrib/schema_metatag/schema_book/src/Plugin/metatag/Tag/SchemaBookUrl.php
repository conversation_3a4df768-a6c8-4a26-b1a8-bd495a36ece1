<?php

namespace Drupal\schema_book\Plugin\metatag\Tag;

use <PERSON>upal\schema_metatag\Plugin\metatag\Tag\SchemaNameBase;

/**
 * Provides a plugin for the 'schema_book_url' meta tag.
 *
 * - 'id' should be a globally unique id.
 * - 'name' should match the Schema.org element name.
 * - 'group' should match the id of the group that defines the Schema.org type.
 *
 * @MetatagTag(
 *   id = "schema_book_url",
 *   label = @Translation("url"),
 *   description = @Translation("REQUIRED BY GOOGLE. URL to the page on your site about the book. The page may list all available editions."),
 *   name = "url",
 *   group = "schema_book",
 *   weight = 0,
 *   type = "string",
 *   secure = FALSE,
 *   multiple = FALSE,
 *   property_type = "url",
 *   tree_parent = {},
 *   tree_depth = -1,
 * )
 */
class SchemaBookUrl extends SchemaNameBase {

}
