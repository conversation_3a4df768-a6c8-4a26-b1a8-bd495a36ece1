(function ($, Drupal) {
  Drupal.behaviors.popupMessage = {
    attach: function (context, settings) {
      $(document).ready(function () {
        // Get the current path.
        const path = window.location.pathname;

        // Check if the path matches specific product pages.
        if (path === '/product/9' || path === '/product/10' || path === '/product/11') {
          // Create the popup content.
          const popupHtml = `
            <div id="product-popup" class="popup-overlay">
              <div class="popup-content">
                <p>
                  A period of 2 months must elapse after the registration of your CreaBOOK 
                  before starting a certification process. Please renew your request in good time.
                </p>
                <button id="popup-close">Close</button>
              </div>
            </div>
          `;

          // Append the popup to the body.
          $('body').append(popupHtml);

          // Show the popup.
          $('#product-popup').fadeIn();

          // Close button functionality.
          $('#popup-close').on('click', function () {
            $('#product-popup').fadeOut(function () {
              $(this).remove();
            });
          });
        }
      });
    },
  };
})(j<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>);
