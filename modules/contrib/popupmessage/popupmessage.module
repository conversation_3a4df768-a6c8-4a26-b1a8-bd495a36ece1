<?php

use <PERSON><PERSON><PERSON>\node\NodeInterface;

/**
 * Implements hook_node_view().
 */
function popupmessage_node_view(array &$build, NodeInterface $node, $view_mode) {
  // Check if the content is a product and matches specific product IDs.
  if ($node->bundle() === 'product' && in_array($node->id(), [9, 10, 11])) {
    // Attach the popup library.
    $build['#attached']['library'][] = 'popupmessage/popup_message';
  }
}
