<?xml version="1.0"?>
<ruleset name="profile">
  <description>Default PHP CodeSniffer configuration for Profile.</description>
  <file>.</file>
  <arg name="extensions" value="php,module,inc,install,test,profile,theme"/>
  <config name="drupal_core_version" value="9"/>

  <exclude-pattern>*/.git/*</exclude-pattern>
  <exclude-pattern>*/config/*</exclude-pattern>
  <exclude-pattern>*/css/*</exclude-pattern>
  <exclude-pattern>*/js/*</exclude-pattern>
  <exclude-pattern>*/icons/*</exclude-pattern>
  <exclude-pattern>README.md</exclude-pattern>

  <rule ref="Drupal">
    <!-- Profile is not yet compliant with the new deprecation format. -->
    <exclude name="Drupal.Commenting.Deprecated"/>
    <exclude name="Drupal.Semantics.FunctionTriggerError"/>

    <!-- The following rules are intentionally disabled. -->
    <exclude name="Drupal.Commenting.ClassComment.Missing"/>
    <exclude name="Drupal.Files.LineLength.TooLong"/>
    <exclude name="Drupal.Arrays.Array.LongLineDeclaration"/>
    <exclude name="Drupal.Semantics.FunctionT.NotLiteralString"/>
    <!-- Complains about example code. -->
    <exclude name="Generic.CodeAnalysis.UselessOverridingMethod.Found"/>
    <!-- Complains about tests which don't need short descriptions. -->
    <exclude name="Drupal.Commenting.DocComment.MissingShort"/>
    <exclude name="Drupal.Commenting.DocComment.ShortNotCapital"/>
    <exclude name="Drupal.Commenting.DocComment.ShortSingleLine"/>
    <!-- Complains about constraint plugins. -->
    <exclude name="Drupal.Commenting.VariableComment"/>
    <!-- Complains about config entity types. -->
    <exclude name="Drupal.NamingConventions.ValidVariableName.LowerCamelName"/>
  </rule>

</ruleset>
