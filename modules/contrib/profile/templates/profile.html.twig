{#
/**
 * @file
 *
 * Default theme implementation for profiles.
 *
 * Available variables:
 * - content: Items for the content of the profile.
 *   Use 'content' to print them all, or print a subset such as
 *   'content.title'. Use the following code to exclude the
 *   printing of a given child element:
 *   @code
 *   {{ content|without('title') }}
 *   @endcode
 * - attributes: HTML attributes for the wrapper.
 * - view_mode: The profile view mode used.
 * - profile: The profile object.
 * - url: The profile URL, if available.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
  'profile',
  'profile--' ~ profile.id,
  'profile--type--' ~ profile.bundle|clean_class,
  profile.isDefault() ? 'profile--is-default',
  view_mode ? 'profile--view-mode--' ~ view_mode|clean_class,
  'clearfix',
]
%}
<div{{ attributes.addClass(classes) }}>
  {{ title_suffix.contextual_links }}
  {{ content }}
</div>
