<?php

/**
 * @file
 * Install, update and uninstall functions for the profile module.
 */

use Drupal\Core\Field\BaseFieldDefinition;
use <PERSON><PERSON><PERSON>\profile\ProfileStorageSchema;
use Drupal\user\Entity\Role;

/**
 * Implements hook_update_dependencies().
 */
function profile_update_dependencies() {
  $dependencies = [];

  // <PERSON><PERSON><PERSON>'s system update 8501 alters revisionable entities, this uses the
  // entity_keys. The langcode key was removed from the Profile entity in
  // profile_update_8001 but it was not removed from the stored entity_keys. To
  // fix this, update 8003 must run before 8501 is executed.
  $dependencies['system'][8501] = [
    'profile' => 8003,
  ];

  return $dependencies;
}

/**
 * Uninstalls the langcode field from the profile entity.
 */
function profile_update_8001() {
  $definition = \Drupal::entityTypeManager()->getDefinition('profile');

  // Allow langcode field to be null.
  $schema = \Drupal::database()->schema();
  if ($schema->fieldExists($definition->getBaseTable(), 'langcode')) {
    $schema->changeField($definition->getBaseTable(), 'langcode', 'langcode', [
      'type' => 'varchar',
      'length' => 12,
      'not null' => FALSE,
    ]);

    // Set langcode field to null so it can be deleted.
    \Drupal::database()
      ->update($definition->getBaseTable())
      ->fields(['langcode' => NULL])
      ->execute();
  }

  $entity_definition_update = \Drupal::entityDefinitionUpdateManager();
  if ($storage_definition = $entity_definition_update->getFieldStorageDefinition('langcode', 'profile')) {
    $entity_definition_update->uninstallFieldStorageDefinition($storage_definition);
  }

  return t('Language code field uninstalled from profile entity.');
}

/**
 * Updates view profile permission name.
 */
function profile_update_8002() {
  /** @var \Drupal\user\Entity\Role[] $roles */
  $roles = Role::loadMultiple();

  foreach ($roles as $role) {
    if ($role->hasPermission('view profile')) {
      $role->revokePermission('view profile');
      $role->grantPermission('view own profile');
      $role->save();
    }
  }

  return t('Permission name updated.');
}

/**
 * Removes the langcode key from the profile entity keys.
 */
function profile_update_8003() {
  $definition_update_manager = \Drupal::entityDefinitionUpdateManager();
  $profile_entity_type = $definition_update_manager->getEntityType('profile');
  if ($profile_entity_type->hasKey('langcode')) {
    $keys = $profile_entity_type->getKeys();
    unset($keys['langcode']);
    $profile_entity_type->set('entity_keys', $keys);
    $definition_update_manager->updateEntityType($profile_entity_type);
  }

  return t('Language code key removed from profile entity definition.');
}

/**
 * Add the uid entity key to profiles.
 */
function profile_update_8004() {
  $definition_update_manager = \Drupal::entityDefinitionUpdateManager();
  $entity_type = $definition_update_manager->getEntityType('profile');

  // Fix any null `uid` field values.
  $database = \Drupal::database();
  $database->update($entity_type->getBaseTable())
    ->fields(['uid' => 0])
    ->isNull('uid')
    ->execute();
  $database->update($entity_type->getRevisionTable())
    ->fields(['uid' => 0])
    ->isNull('uid')
    ->execute();

  $entity_keys = $entity_type->getKeys();
  $entity_keys['uid'] = 'uid';
  $entity_keys['owner'] = 'uid';
  $entity_type->set('entity_keys', $entity_keys);
  $definition_update_manager->updateEntityType($entity_type);
  $uid_definition = $definition_update_manager->getFieldStorageDefinition('uid', 'profile');
  $definition_update_manager->updateFieldStorageDefinition($uid_definition);

  return t('The uid entity key has been added to profiles.');
}

/**
 * Rerun uid entity key addition after incorrect entity type fixed.
 */
function profile_update_8005() {
  // Rerun the previous update with correction.
  profile_update_8004();
}

/**
 * Install the `data` field.
 */
function profile_update_8006() {
  $storage_definition = BaseFieldDefinition::create('map')
    ->setLabel(t('Data'))
    ->setDescription(t('A serialized array of additional data.'))
    ->setRevisionable(TRUE);

  $update_manager = \Drupal::entityDefinitionUpdateManager();
  $update_manager->installFieldStorageDefinition('data', 'profile', 'profile', $storage_definition);
}

/**
 * Set the 'published' entity key.
 */
function profile_update_8007() {
  $definition_update_manager = \Drupal::entityDefinitionUpdateManager();
  $entity_type = $definition_update_manager->getEntityType('profile');
  $keys = $entity_type->getKeys();
  $keys['published'] = 'status';
  $entity_type->set('entity_keys', $keys);
  $definition_update_manager->updateEntityType($entity_type);
}

/**
 * Adds a storage schema for profile that adds a multi-column index.
 */
function profile_update_8008() {
  $definition_update_manager = \Drupal::entityDefinitionUpdateManager();
  $entity_type = $definition_update_manager->getEntityType('profile');
  $entity_type->setHandlerClass('storage_schema', ProfileStorageSchema::class);
  $definition_update_manager->updateEntityType($entity_type);
}

/**
 * Update revision fields.
 */
function profile_update_8009() {
  $definition_update_manager = \Drupal::entityDefinitionUpdateManager();
  $entity_type = $definition_update_manager->getEntityType('profile');
  $revision_metadata_keys = $entity_type->get('revision_metadata_keys');
  $revision_metadata_keys += [
    'revision_created' => 'revision_created',
    'revision_user' => 'revision_user',
    'revision_log_message' => 'revision_log_message',
  ];
  $entity_type->set('revision_metadata_keys', $revision_metadata_keys);
  $definition_update_manager->updateEntityType($entity_type);

  $revision_created = BaseFieldDefinition::create('created')
    ->setLabel(t('Revision create time'))
    ->setDescription(t('The time that the current revision was created.'))
    ->setRevisionable(TRUE);
  $definition_update_manager->installFieldStorageDefinition('revision_created', $entity_type->id(), 'profile', $revision_created);

  $revision_user = BaseFieldDefinition::create('entity_reference')
    ->setLabel(t('Revision user'))
    ->setDescription(t('The user ID of the author of the current revision.'))
    ->setSetting('target_type', 'user')
    ->setInitialValueFromField('uid')
    ->setRevisionable(TRUE);
  $definition_update_manager->installFieldStorageDefinition('revision_user', $entity_type->id(), 'profile', $revision_user);

  $revision_log_message = BaseFieldDefinition::create('string_long')
    ->setLabel(t('Revision log message'))
    ->setDescription(t('Briefly describe the changes you have made.'))
    ->setRevisionable(TRUE)
    ->setDefaultValue('')
    ->setDisplayOptions('form', [
      'type' => 'string_textarea',
      'weight' => 25,
      'settings' => [
        'rows' => 4,
      ],
    ]);
  $definition_update_manager->installFieldStorageDefinition('revision_log_message', $entity_type->id(), 'profile', $revision_log_message);

  // Use the "changed" timestamp as the initial revision_created.
  $database = \Drupal::database();
  $database->update($entity_type->getRevisionTable())
    ->expression('revision_created', 'changed')
    ->execute();

  return t('The revision metadata fields have been added to profiles.');
}

/**
 * Update the status field.
 */
function profile_update_8010() {
  $definition_update_manager = \Drupal::entityDefinitionUpdateManager();
  $field_definition = $definition_update_manager->getFieldStorageDefinition('status', 'profile');
  $field_definition->setTranslatable(FALSE);
  $definition_update_manager->updateFieldStorageDefinition($field_definition);
}
