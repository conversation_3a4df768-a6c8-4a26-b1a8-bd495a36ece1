langcode: en
status: true
dependencies:
  module:
    - profile
id: profiles
label: Profiles
module: views
description: ''
tag: ''
base_table: profile
base_field: profile_id
display:
  default:
    display_plugin: default
    id: default
    display_title: Master
    position: 0
    display_options:
      access:
        type: none
        options: {  }
      cache:
        type: tag
        options: {  }
      query:
        type: views_query
        options:
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_comment: ''
          query_tags: {  }
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        type: none
        options:
          offset: 0
      style:
        type: grid
        options:
          grouping: {  }
          columns: 4
          automatic_width: true
          alignment: horizontal
          col_class_default: true
          col_class_custom: ''
          row_class_default: true
          row_class_custom: ''
      row:
        type: fields
        options:
          inline: {  }
          separator: ''
          hide_empty: false
          default_field_elements: true
      fields:
        rendered_entity:
          id: rendered_entity
          table: profile
          field: rendered_entity
          relationship: none
          group_type: group
          admin_label: ''
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          view_mode: default
          entity_type: profile
          plugin_id: rendered_entity
        operations:
          id: operations
          table: profile
          field: operations
          relationship: none
          group_type: group
          admin_label: ''
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          destination: true
          entity_type: profile
          plugin_id: entity_operations
      filters: {  }
      sorts:
        is_default:
          id: is_default
          table: profile
          field: is_default
          relationship: none
          group_type: group
          admin_label: ''
          order: DESC
          exposed: false
          expose:
            label: ''
          entity_type: profile
          entity_field: is_default
          plugin_id: standard
        profile_id:
          id: profile_id
          table: profile
          field: profile_id
          relationship: none
          group_type: group
          admin_label: ''
          order: DESC
          exposed: false
          expose:
            label: ''
          entity_type: profile
          entity_field: profile_id
          plugin_id: standard
      header: {  }
      footer: {  }
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          empty: true
          tokenize: false
          content:
            value: 'There are no profiles yet.'
            format: basic_html
          plugin_id: text
      relationships: {  }
      arguments:
        uid:
          id: uid
          table: profile
          field: uid
          relationship: none
          group_type: group
          admin_label: ''
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: user
          default_argument_options:
            user: false
          summary_options:
            base_path: ''
            count: true
            items_per_page: 25
            override: false
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
          entity_type: profile
          entity_field: uid
          plugin_id: numeric
        type:
          id: type
          table: profile
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          default_action: ignore
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            items_per_page: 25
            override: false
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: 'entity:profile_type'
            fail: 'not found'
          validate_options: {  }
          glossary: false
          limit: 0
          case: none
          path_case: none
          transform_dash: false
          break_phrase: false
          entity_type: profile
          entity_field: type
          plugin_id: string
        status:
          id: status
          table: profile
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          default_action: ignore
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            items_per_page: 25
            override: false
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
          entity_type: profile
          entity_field: status
          plugin_id: numeric
      display_extenders: {  }
      title: Profiles
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
      tags: {  }
  user_page:
    display_plugin: embed
    id: user_page
    display_title: 'User page'
    position: 1
    display_options:
      display_extenders: {  }
      display_description: ''
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
      tags: {  }
