<?php

namespace Drupal\profile;

use <PERSON>upal\Core\Entity\EntityInterface;
use Drupal\Core\Entity\EntityViewBuilder;

/**
 * Render controller for profile entities.
 */
class ProfileViewBuilder extends EntityViewBuilder {

  /**
   * {@inheritdoc}
   */
  protected function getBuildDefaults(EntityInterface $entity, $view_mode) {
    $defaults = parent::getBuildDefaults($entity, $view_mode);
    $defaults['#theme'] = 'profile';
    return $defaults;
  }

}
