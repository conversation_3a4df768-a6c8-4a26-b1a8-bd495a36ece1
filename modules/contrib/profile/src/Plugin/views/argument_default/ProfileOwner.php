<?php

namespace Drupal\profile\Plugin\views\argument_default;

use <PERSON><PERSON><PERSON>\Core\Cache\Cache;
use <PERSON><PERSON>al\Core\Cache\CacheableDependencyInterface;
use <PERSON><PERSON>al\profile\Entity\ProfileInterface;
use Drupal\views\Plugin\views\argument_default\ArgumentDefaultPluginBase;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Default argument plugin to extract a user from a profile.
 *
 * @ViewsArgumentDefault(
 *   id = "profile_owner",
 *   title = @Translation("User ID from profile route match")
 * )
 */
class ProfileOwner extends ArgumentDefaultPluginBase implements CacheableDependencyInterface {

  /**
   * The route match.
   *
   * @var \Drupal\Core\Routing\RouteMatchInterface
   */
  protected $routeMatch;

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    $instance = parent::create($container, $configuration, $plugin_id, $plugin_definition);
    $instance->routeMatch = $container->get('current_route_match');
    return $instance;
  }

  /**
   * {@inheritdoc}
   */
  public function getArgument() {
    if (($profile = $this->routeMatch->getParameter('profile')) && $profile instanceof ProfileInterface) {
      return $profile->getOwnerId();
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getCacheMaxAge() {
    return Cache::PERMANENT;
  }

  /**
   * {@inheritdoc}
   */
  public function getCacheContexts() {
    return ['url'];
  }

}
