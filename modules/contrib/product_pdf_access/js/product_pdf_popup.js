(function ($, Drupal) {
  Drupal.behaviors.productPdfPopup = {
    attach: function (context, settings) {
      if (settings.product_pdf_access && settings.product_pdf_access.showPopup) {
        const fileName = settings.product_pdf_access.fileName;
        const downloadLink = 'https://creafree.org/doc/' + fileName;

        // Create the popup content.
        const popupContent = `
          <div id="product-popup" class="product-popup">
            <div class="popup-content">
              <h2>Download your product</h2>
              <p>Your product is ready for download. Click the link below:</p>
              <a href="${downloadLink}" target="_blank">Download ${fileName}</a>
              <button id="close-popup">Close</button>
            </div>
          </div>
        `;

        // Append the popup to the body and show it.
        $('body').append(popupContent);
        $('#product-popup').fadeIn();

        // Close the popup on button click.
        $('#close-popup').on('click', function () {
          $('#product-popup').fadeOut(function () {
            $(this).remove();
          });
        });
      }
    }
  };
})(j<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>);
