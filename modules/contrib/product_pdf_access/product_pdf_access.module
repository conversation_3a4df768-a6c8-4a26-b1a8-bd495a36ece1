<?php

use <PERSON><PERSON>al\Core\Mail\MailManagerInterface;
use Drupal\Core\Logger\LoggerChannelFactoryInterface;
use <PERSON>upal\user\Entity\User;
use Drupal\commerce_order\Entity\OrderInterface;
use Drupal\commerce_product\Entity\ProductVariation;
use Drupal\Core\File\FileSystemInterface;
use Drupal\Core\Render\BubbleableMetadata;
use Drupal\Core\Render\AttachmentsInterface;
use Symfony\Component\Mailer\Mailer;
use Symfony\Component\Mailer\Transport\Smtp\EsmtpTransport;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mime\Part\DataPart;
use Symfony\Component\Mime\Part\File;

/**
 * Implements hook_commerce_order_update().
 */
/**
 * Implements hook_commerce_order_update().
 */
function product_pdf_access_commerce_order_update(OrderInterface $order) {
  // Check if the order is in the "completed" state.
  if ($order->getState()->value === 'completed') {
    \Drupal::logger('product_pdf_access')->notice('Order @order_id is completed', ['@order_id' => $order->id()]);

    // Load the user associated with the order.
    $user_id = $order->getCustomerId();
    $user = User::load($user_id);

    if ($user) {
      foreach ($order->getItems() as $item) {
        $purchased_entity = $item->getPurchasedEntity();
        if ($purchased_entity instanceof ProductVariation) {
          $variation_id = $purchased_entity->id();
          $restricted_variation_ids = [2, 3, 4, 6];

          if (in_array($variation_id, $restricted_variation_ids)) {
            \Drupal::logger('product_pdf_access')->notice('Product variation ID @variation_id found in order @order_id', [
              '@variation_id' => $variation_id,
              '@order_id' => $order->id()
            ]);

            // 🟠 Dodanie roli 'member' i aktualizacja pola 'field_custom_role'
            if (!$user->hasRole('member')) {
              $user->addRole('member');
              \Drupal::logger('product_pdf_access')->info('Role "member" added to user @uid.', ['@uid' => $user->id()]);
            }

            $custom_role_value = $user->get('field_custom_role')->value;
            if ($custom_role_value && substr($custom_role_value, -2) !== '-m') {
              $user->set('field_custom_role', $custom_role_value . '-m');
            } elseif (empty($custom_role_value)) {
              $user->set('field_custom_role', 'member-m');
            }
            $user->save();

            // Wysyłanie e-maila z dokumentem
            product_pdf_access_send_doc_email($user, $variation_id, $order->id());
            break; // Wyślij tylko raz, nawet jeśli jest kilka pasujących wariantów
          }
        }
      }
    } else {
      \Drupal::logger('product_pdf_access')->error('User could not be loaded for order @order_id', ['@order_id' => $order->id()]);
    }
  }
}



/**
 * Returns the file name based on the variation ID.
 */
function product_pdf_access_get_file_name($variation_id) {
  switch ($variation_id) {
    case 4:
      return 'creamakerbasic.docx';
    case 2:
      return 'creamakersilver.docx';
    case 3:
      return 'creamakergold.docx';
    case 6:
      return 'creasafe.docx';
    default:
      return '';
  }
}

/**
 * Sends an email with the DOC attachment to the customer using Symfony Mailer.
 */
/**
 * Sends an email with the DOC attachment to the customer using Symfony Mailer.
 */
function product_pdf_access_send_doc_email(User $user, $variation_id, $order_id) {
  
  // Retrieve SMTP credentials from configuration.
  $config = \Drupal::config('creabook_certificate_notify.settings');
  $smtp_username = $config->get('smtp_username');
  $smtp_password = $config->get('smtp_password');

  if (empty($smtp_username) || empty($smtp_password)) {
    \Drupal::logger('creafreemaker_pdf_notification')->error('SMTP username or password is not configured.');
    return;
  }

  // Set up the SMTP transport for mail.
  $transport = new EsmtpTransport('smtp.gmail.com', 587);
  $transport->setUsername($smtp_username)
            ->setPassword($smtp_password);

  // Initialize the Symfony mailer.
  $mailer = new Mailer($transport);

  // Prepare variables for email based on the variation ID.
  $file_name = '';
  $subject = '';
  $message_body = '';

  switch ($variation_id) {
    case 4:
      $file_name = 'creamakerbasic.docx';
      $subject = 'Product CreaMAKER BASIC';
      $message_body  = '<h1>Congratulations ' . $user->getDisplayName() . '!</h1>';
      $message_body .= '<p>You have purchased product <strong>CreaMAKER BASIC</strong>.</p>';
      $message_body .= '<p>Please find the attached document related to <strong>CreaMAKER BASIC</strong>.</p>';
      break;
    case 2:
      $file_name = 'creamakersilver.docx';
      $subject = 'Product CreaMAKER SILVER';
      $message_body  = '<h1>Congratulations ' . $user->getDisplayName() . '!</h1>';
      $message_body .= '<p>You have purchased product <strong>CreaMAKER SILVER</strong>.</p>';
      $message_body .= '<p>Please find the attached document related to <strong>CreaMAKER SILVER</strong>.</p>';
      break;
    case 3:
      $file_name = 'creamakergold.docx';
      $subject = 'Product CreaMAKER GOLD';
      $message_body = '<h1>Congratulations ' . $user->getDisplayName() . '!</h1>';
      $message_body .= '<p>You have purchased product <strong>CreaMAKER GOLD</strong>.</p>';
      $message_body .= '<p>Please find the attached document related to <strong>CreaMAKER GOLD</strong>.</p>';
      break;
     case 6:
  $file_name = 'creasafe.docx';
  $subject = 'Product Creasafe';
  $message_body = '<h1>Congratulations ' . $user->getDisplayName() . '!</h1>';
  $message_body .= '<p>You have purchased product <strong>CreaSAFE</strong>.</p>';
  $message_body .= '<p>Please find the attached document related to <strong>CreaSAFE</strong>.</p>';
  $message_body .= '<p>Download the letter by clicking on this link <a href="https://creafree.org/letter.pdf" style="color: blue; text-decoration: underline;">Letter of president</a>.</p>';
  break;

    default:
      \Drupal::logger('product_pdf_access')->error('Unknown product variation ID @variation_id for user @uid in order @order_id', [
        '@variation_id' => $variation_id,
        '@uid' => $user->id(),
        '@order_id' => $order_id
      ]);
      return;
  }

  // Continue with the rest of the email sending logic...



  // Define the path to the document based on the variation.
  $existing_file_path = 'public://' . $file_name;
  
  //$pdf_path = 'public://' . $file_name_pdf;

  // // Ensure that the file exists.
  // if (!file_exists($pdf_path)) {
  //   \Drupal::logger('product_pdf_access')->error('Document not found at @path.', ['@path' => $pdf_path]);
  //   return;
  // }

  // Ensure that the file exists.
  if (!file_exists($existing_file_path)) {
    \Drupal::logger('product_pdf_access')->error('Document not found at @path.', ['@path' => $existing_file_path]);
    return;
  }

  // Read the file content and prepare it for attachment.
  $file_content = file_get_contents($existing_file_path);
  // $file_contentpdf = file_get_contents($pdf_path);
  $temp_file_path = 'temporary://' . $file_name;
 // $temp_file_pathpdf = 'temporary://' . $file_name_pdf;
  file_put_contents($temp_file_path, $file_content,);
  //file_put_contents($temp_file_pathpdf,$file_contentpdf,);

  // Ensure the file is readable.
  if (!is_readable($temp_file_path)) {
    \Drupal::logger('product_pdf_access')->error('File not readable at @path.', ['@path' => $temp_file_path]);
    return;
  }

  //  // Ensure the file is readable.
  // if (!is_readable($temp_file_pathpdf)) {
  //   \Drupal::logger('product_pdf_access')->error('File not readable at @path.', ['@path' => $temp_file_pathpdf]);
  //   return;
  // }

  // Create the email with the attached file.
  $email = (new Email())
    ->from('<EMAIL>') // Change this to your actual sender address.
    ->to($user->getEmail())
    ->subject($subject)
    ->html($message_body);

  // Attach the file.
  $attachment = new DataPart(fopen($temp_file_path, 'r'), $file_name);
  // $attachment = new DataPart(fopen($temp_file_pathpdf, 'r'), $file_name_pdf);
  $email->attachPart($attachment);

  // Try to send the email.
  try {
    $mailer->send($email);
    \Drupal::logger('product_pdf_access')->notice('Successfully sent email to user @uid for product variation @variation_id.', [
      '@uid' => $user->id(),
      '@variation_id' => $variation_id
    ]);
  } catch (\Exception $e) {
    \Drupal::logger('product_pdf_access')->error('Failed to send email to user @uid for product variation @variation_id. Error: @error', [
      '@uid' => $user->id(),
      '@variation_id' => $variation_id,
      '@error' => $e->getMessage()
    ]);
  }

  // Clean up the temporary file.
  unlink($temp_file_path);
  //unlink($temp_file_pathpdf);
}

/**
 * Implements hook_mail().
 */
function product_pdf_access_mail($key, &$message, $params) {
  switch ($key) {
    case 'product_pdf':
      $message['subject'] = $params['subject'];
      $message['body'][] = $params['message'];

      // Attach the document if present.
      if (!empty($params['attachments'])) {
        foreach ($params['attachments'] as $attachment) {
          $message['params']['attachments'][] = $attachment;
        }
      }
      break;
  }
}
