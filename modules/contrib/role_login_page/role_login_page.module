<?php

use <PERSON><PERSON>al\Core\Url;
use <PERSON><PERSON>al\user\Entity\User;
use Symfony\Component\HttpFoundation\RedirectResponse;

/**
 * @file
 */

/**
 * Implements hook_help().
 */
function role_login_page_help($page, $arg) {
  switch ($page) {
    case 'admin/help#role_login_page':
      return t('This module is designed to create multiple login pages based on roles.
                The new login page will have everything configurable from the backend, i.e, "username field label", "password field label", "Error messages" and more.
                For example : If the new login page will have role "A" assigned to it then the users with role "A" can only login through this page.');
  }
}

/**
 * Implements hook_permission().
 */
function role_login_page_permission() {
  return [
    'administer role login settings' => [
      'title' => t('Administer Role Login Setings'),
      'description' => t('Configure which roles will be associated with which pages.'),
    ],
  ];
}

/**
 * Validate the allowed roles for this page.
 */
function _role_login_page_validate_login_roles($uid, $roles) {
  $user = User::load($uid);
  $user_roles = $user->getRoles();
  foreach ($user_roles as $role_index => $role_val) {
    if (in_array($role_val, $roles)) {
      return TRUE;
    }
  }
  return FALSE;
}

/**
 *
 * @param type $url
 * @param type $type
 * Clear the cache after creating or updating a login page.
 * @global type $base_url
 */
function _role_login_page_settings_cache_clear($url, $type) {
  global $base_url;
  $custom_page = $base_url . '/' . $url;
  if ($type == 'add') {
    \Drupal::messenger()
      ->addStatus(t('The page <b>@url</b> has been created and is active. This page can now be accessed only as an anonymous user.', ['@url' => $custom_page]));
  }
  elseif ($type == 'update') {
    \Drupal::messenger()
      ->addStatus(t('The page <b>@url</b> has been updated. This page can now be accessed only as an anonymous user.', ['@url' => $custom_page]));
  }
  elseif ($type == 'delete') {
    \Drupal::messenger()
      ->addStatus(t('The page <b>@url</b> has been deleted.', ['@url' => $custom_page]));
  }
  drupal_flush_all_caches();
  $redirect = new RedirectResponse(Url::fromUserInput('/admin/config/login/role_login_settings/list')
    ->toString());
  $redirect->send();
}
