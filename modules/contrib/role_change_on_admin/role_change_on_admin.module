<?php

use Dr<PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\user\Entity\User;

/**
 * Implements hook_user_update().
 *
 * This hook is invoked after a user entity is updated, allowing you to act upon it.
 */
function role_change_on_admin_user_update(User $user) {
  // Only proceed if the user entity is valid.
  if (!$user instanceof User) {
    return;
  }

  // Retrieve the previously stored roles for comparison.
  $old_roles = \Drupal::state()->get('role_change_on_admin_roles_' . $user->id());
  if ($old_roles === NULL) {
    return;
  }

  $new_roles = $user->getRoles();

  // Check if the creator role has been removed.
  if (!in_array('member', $new_roles) && in_array('member', $old_roles)) {
    $custom_role_field = $user->get('field_custom_role')->value;
    if ($custom_role_field) {
      // Remove the '-c' suffix if it exists.
      $suffix = '-m';
      if (substr($custom_role_field, -strlen($suffix)) === $suffix) {
        $new_value = substr($custom_role_field, 0, -strlen($suffix));
        $user->set('field_custom_role', $new_value);
        $user->save();
        \Drupal::logger('role_change_on_admin')->info('Removed suffix -m from field_custom_role for user @uid on role removal by admin.', ['@uid' => $user->id(), '@value' => $new_value]);
      }
    }
  }

  // Update the stored roles for future comparisons.
  \Drupal::state()->set('role_change_on_admin_roles_' . $user->id(), $new_roles);
}

/**
 * Implements hook_form_alter().
 *
 * Adds a custom submit handler to store old roles before user form submission.
 */
function role_change_on_admin_form_alter(array &$form, FormStateInterface $form_state, $form_id) {
  if ($form_id === 'user_form') {
    // Add a custom submit handler to store the roles before the form is submitted.
    $form['actions']['submit']['#submit'][] = 'role_change_on_admin_user_form_submit';
    
    // Store old roles in the form state.
    $form_state->set('roles_old', $form_state->getFormObject()->getEntity()->getRoles());
  }
}

/**
 * Custom submit handler for user_form.
 */
function role_change_on_admin_user_form_submit(array &$form, FormStateInterface $form_state) {
  // Get the user entity from the form state.
  $user = $form_state->getFormObject()->getEntity();
  
  // Save the roles before update.
  \Drupal::state()->set('role_change_on_admin_roles_' . $user->id(), $form_state->getValue('roles'));
}
