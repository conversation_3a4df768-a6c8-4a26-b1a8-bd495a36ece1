<?php

namespace Drupal\role_request;

use <PERSON><PERSON><PERSON>\user\Entity\Role;
use <PERSON><PERSON><PERSON>\user\Entity\User;

/**
 * Service to manage role changes.
 */
class RoleChangeService {

  /**
   * Constructs a RoleChangeService object.
   */
  public function __construct() {
    // No dependencies required for this service at the moment.
  }

  /**
   * Processes the request to become a participant.
   *
   * @param int $user_id
   *   The user ID of the requesting user.
   */
  public function requestParticipant($user_id) {
    $user = User::load($user_id);

    // Ensure the user exists and has the creator role.
    if ($user && $user->hasRole('creator')) {
      // Add the '-p' suffix to the field_custom_role if not already present.
      $custom_role_field = $user->get('field_custom_role')->value;

      // Ensure '-c' suffix is present
      if (strpos($custom_role_field, '-c') === false) {
        $custom_role_field .= '-c';
      }

      // Add '-p' suffix if not already present
      if (strpos($custom_role_field, '-p') === false) {
        $custom_role_field .= '-p';
      }

      // Add the 'participants' role if not already assigned.
      if (!$user->hasRole('participants')) {
        $user->addRole('participants');
      }

      // Set the updated custom role field.
      $user->set('field_custom_role', $custom_role_field);

      // Save the updated user entity.
      $user->save();

      \Drupal::logger('role_request')->info('User @uid requested participant role. Updated field_custom_role to @value.', [
        '@uid' => $user->id(),
        '@value' => $custom_role_field,
      ]);
    }
  }
}
