<?php

namespace Dr<PERSON>al\role_request\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use <PERSON>ymfony\Component\DependencyInjection\ContainerInterface;
use <PERSON><PERSON><PERSON>\role_request\RoleChangeService;
use Symfony\Component\HttpFoundation\RedirectResponse;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;

/**
 * Controller for handling role requests.
 */
class RoleRequestController extends ControllerBase {

  /**
   * The role change service.
   *
   * @var \Drupal\role_request\RoleChangeService
   */
  protected $roleChangeService;

  /**
   * Constructs a RoleRequestController object.
   *
   * @param \Drupal\role_request\RoleChangeService $role_change_service
   *   The role change service.
   */
  public function __construct(RoleChangeService $role_change_service) {
    $this->roleChangeService = $role_change_service;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('role_request.role_change')
    );
  }

  /**
   * Displays the form to request participant role.
   *
   * @return array
   *   A render array representing the request form.
   */
  public function requestForm() {
    $current_user = $this->currentUser();

    // Check if the current user has the 'creator' role.
    if ($current_user->hasRole('creator')) {
      $build['description'] = [
        '#markup' => $this->t('You are currently a creator. Click the button below to request the participant role.'),
      ];

      $build['form'] = [
        '#type' => 'form',
        '#attributes' => [
          'id' => 'role_request_form',
        ],
        'actions' => [
          '#type' => 'actions',
          'submit' => [
            '#type' => 'submit',
            '#value' => $this->t('Request Participant Role'),
            '#button_type' => 'primary',
            '#submit' => [[$this, 'submitRequest']],
          ],
        ],
      ];

      return $build;
    }
    else {
      return [
        '#markup' => $this->t('Only users with the creator role can request the participant role.'),
      ];
    }
  }

  /**
   * Handles the submission of the participant role request.
   *
   * @param array $form
   *   An associative array containing the structure of the form.
   * @param \Drupal\Core\Form\FormStateInterface $form_state
   *   The current state of the form.
   *
   * @return \Symfony\Component\HttpFoundation\RedirectResponse
   *   A redirect response to the current page.
   */
  public function submitRequest(array &$form, FormStateInterface $form_state) {
    $user = $this->currentUser();

    // Use the role change service to update the user's role and field.
    $this->roleChangeService->requestParticipant($user->id());

    // Display a message to the user.
    $this->messenger()->addMessage($this->t('Your request to become a participant has been processed.'));

    // Redirect back to the same page.
    return new RedirectResponse('/request-participant');
  }
}
