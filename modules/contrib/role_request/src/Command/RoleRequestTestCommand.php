<?php

namespace Drupal\role_request\Command;

use Drush\Commands\DrushCommands;

class RoleRequestTestCommand extends DrushCommands {

  /**
   * Tests the RoleChangeService.
   *
   * @command role_request:test
   * @description Tests the RoleChangeService class.
   */
  public function testRoleChangeService() {
    try {
      // Load the RoleChangeService service.
      $service = \Drupal::service('role_request.role_change');

      // Check if the service is instantiated successfully.
      if ($service instanceof \Drupal\role_request\RoleChangeService) {
        $this->output()->writeln("RoleChangeService is instantiated successfully.");
      } else {
        $this->output()->writeln("RoleChangeService is not found.");
      }
    } catch (\Exception $e) {
      $this->output()->writeln("Error: " . $e->getMessage());
    }
  }
}
