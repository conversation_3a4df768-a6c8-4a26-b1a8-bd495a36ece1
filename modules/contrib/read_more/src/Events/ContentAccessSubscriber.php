<?php

namespace Drupal\read_more\EventSubscriber;

use Drupal\Core\Session\AccountInterface;
use S<PERSON>fony\Component\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>al\node\NodeInterface;
use Dr<PERSON>al\node\Event\NodeEvent;
use Drupal\node\NodeEvents;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Listens to node events.
 */
class ContentAccessSubscriber implements EventSubscriberInterface {

  /**
   * {@inheritdoc}
   */
  public static function getSubscribedEvents() {
    $events[NodeEvents::NODE_ACCESS][] = ['checkAccess'];
    return $events;
  }

  /**
   * Checks access for viewing node content.
   */
  public function checkAccess(NodeEvent $event) {
    $node = $event->getNode();
    $user = \Drupal::currentUser();
    
    if ($node->getType() === 'creabook') {
      if (!$user->isAuthenticated() || (!$user->hasRole('creator') && !$user->hasRole('participants'))) {
        $event->setAccess(FALSE);
      }
    }
  }
}
