<?php

/**
 * Implements hook_preprocess_node().
 */
function read_more_preprocess_node(array &$variables) {
  // Check if the node is of type 'creabook' and the view mode is 'teaser'.
  if ($variables['node']->getType() === 'creabook' && $variables['view_mode'] === 'teaser') {
    // Get the current user.
    $user = \Drupal::currentUser();

    // Initialize the read more link variable.
    $read_more_link = '';

    // Determine link behavior based on user authentication.
    if ($user->isAuthenticated()) {
      // Direct link to the full node page if the user is authenticated.
      $read_more_link = '<a href="' . $variables['node']->toUrl()->toString() . '" class="read-more-link">Read More</a>';
    } else {
      // Redirect to the login page if the user is not authenticated.
      //$read_more_link = '<a href="/user/login" class="read-more-link">Log in to read more</a>';
       $read_more_link = '<a href="#" class="read-more-link" data-bs-toggle="modal" data-bs-target="#loginModal">Read More</a>';
    }

    // Append the read more link to the node content safely.
    $variables['content']['read_more_link'] = [
      '#markup' => $read_more_link,
      '#allowed_tags' => ['a'],
      '#weight' => 2, // Set a weight to control the order.
    ];
  }
}
