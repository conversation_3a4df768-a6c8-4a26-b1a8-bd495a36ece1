document.addEventListener('DOMContentLoaded', function () {
  var readMoreLinks = document.querySelectorAll('.read-more-link');

  readMoreLinks.forEach(function (link) {
    if (!link.dataset.readMoreInitialized) {
      link.addEventListener('click', function (event) {
        event.preventDefault();
        alert('Read more clicked!');
      });

      // Set a data attribute to indicate the initialization.
      link.dataset.readMoreInitialized = 'true';
    }
  });
});
