<?php

/**
 * Implements hook_page_attachments().
 */
function product_availability_popup_page_attachments(array &$attachments) {
  // Load the current path.
  $current_path = \Drupal::service('path.current')->getPath();
  $product_path = '/product/9'; // The path to check for the specific product.

  // Check if the current path matches the product path.
  if ($current_path === $product_path) {
    // Attach the custom JavaScript file.
    $attachments['#attached']['library'][] = 'product_availability_popup/product_popup';
  }
}
