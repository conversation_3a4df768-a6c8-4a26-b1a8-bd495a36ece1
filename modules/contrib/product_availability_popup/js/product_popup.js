(function (Drupal, once) {
    Drupal.behaviors.productAvailabilityPopup = {
      attach: function (context, settings) {
        once('productAvailabilityPopup', 'body', context).forEach(function (element) {
          // Function to display the popup message.
          if (confirm('Digital product not yet available. Would you like to go back to the Services page?')) {
            // Redirect to the "services2" page if the user confirms.
            window.location.href = '/services2';
          }
        });
      }
    };
  })(Drupal, once);
  