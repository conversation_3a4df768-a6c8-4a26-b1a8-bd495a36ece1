<?php

namespace Drupal\simple_sitemap\Plugin\simple_sitemap;

use <PERSON>upal\Core\Plugin\PluginBase;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides a base class for Simple XML Sitemap plugins.
 */
abstract class SimpleSitemapPluginBase extends PluginBase implements SimpleSitemapPluginInterface {

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition): SimpleSitemapPluginBase {
    return new static($configuration, $plugin_id, $plugin_definition);
  }

  /**
   * {@inheritdoc}
   */
  public function label(): string {
    return $this->getPluginDefinition()['label'];
  }

  /**
   * {@inheritdoc}
   */
  public function description(): string {
    return $this->getPluginDefinition()['description'];
  }

  /**
   * {@inheritdoc}
   */
  public function settings(): array {
    return $this->getPluginDefinition()['settings'];
  }

}
