simple_sitemap.engines.status:
  path: '/admin/config/search/simplesitemap/engines'
  defaults:
    _entity_list: 'simple_sitemap_engine'
    _title: 'Simple XML Sitemap'
  requirements:
    _permission: 'administer sitemap settings'

simple_sitemap.engines.settings:
  path: '/admin/config/search/simplesitemap/engines/settings'
  defaults:
    _form: '\Drupal\simple_sitemap_engines\Form\SimplesitemapEnginesForm'
    _title: 'Simple XML Sitemap'
  requirements:
    _permission: 'administer sitemap settings'

simple_sitemap.engines.index_now.key:
  path: '/simple_sitemap_engines/index_now_key/{key}'
  defaults:
    _controller: '\Drupal\simple_sitemap_engines\Controller\IndexNowController::getKeyFile'
    _disable_route_normalizer: 'TRUE'
  requirements:
    # IndexNow key must be accessible for search engines.
    _access: 'TRUE'
