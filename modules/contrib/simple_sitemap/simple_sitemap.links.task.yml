simple_sitemap.sitemaps:
  route_name: entity.simple_sitemap.collection
  title: 'Sitemaps'
  base_route: entity.simple_sitemap.collection
  weight: -1

simple_sitemap.status:
  route_name: entity.simple_sitemap.collection
  title: 'Status'
  parent_id: simple_sitemap.sitemaps
  weight: -1

simple_sitemap.types:
  route_name: entity.simple_sitemap_type.collection
  title: 'Types'
  parent_id: simple_sitemap.sitemaps
  weight: 0

simple_sitemap.settings:
  route_name: simple_sitemap.settings
  title: 'Settings'
  parent_id: simple_sitemap.sitemaps
  weight: 1

simple_sitemap.inclusion:
  route_name: simple_sitemap.entities
  title: 'Inclusion'
  base_route: entity.simple_sitemap.collection
  weight: 1

simple_sitemap.entities:
  route_name: simple_sitemap.entities
  title: 'Entities'
  parent_id: simple_sitemap.inclusion
  weight: -1

simple_sitemap.custom:
  route_name: simple_sitemap.custom
  title: 'Custom links'
  parent_id: simple_sitemap.inclusion
  weight: 0
