build:
  assessment:
    validate_codebase:
      phplint:
      container_composer:
      phpcs:
        # phpcs will use core's specified version of Coder.
        sniff-all-files: true
        halt-on-fail: false
    testing:
      # run_tests task is executed several times in order of performance speeds.
      # halt-on-fail can be set on the run_tests tasks in order to fail fast.
      # suppress-deprecations is false in order to be alerted to usages of
      # deprecated code.
      run_tests.standard:
        types: 'Simpletest,PHPUnit-Unit,PHPUnit-Kernel,PHPUnit-Functional'
        testgroups: '--all'
        suppress-deprecations: false
      run_tests.js:
        types: 'PHPUnit-FunctionalJavascript'
        testgroups: '--all'
        suppress-deprecations: false
      nightwatchjs: {  }
