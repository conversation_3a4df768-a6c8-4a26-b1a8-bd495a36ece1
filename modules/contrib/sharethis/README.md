# ShareThis

The Sharethis module provides social bookmarking utility on selected node types.

For a full description of the module, visit the
[project page](https://www.drupal.org/project/sharethis).

Submit bug reports and feature suggestions, or track changes in the
[issue queue](https://www.drupal.org/project/issues/sharethis).


## Contents of this file

- Requirements
- Installation
- Configuration
- Known issues
- Maintainers


## Requirements

This module requires no modules outside of Drupal core.


## Installation

Install as you would normally install a contributed Drupal module. See:
[Installing Drupal Modules](https://www.drupal.org/docs/extending-drupal/installing-drupal-modules)
for further information.


## Configuration

Configure the Sharethis Module settings in

`Administration » Configuration » Services » Sharethis`


## Known issues

If using the Metatag module, if the "referrer" meta tag is set to
"no-referral" the email share functionality will fail to work properly.


## Maintainers

- Naveen Valecha - [naveenvalecha](https://drupal.org/u/naveenvalecha)
- <PERSON><PERSON><PERSON><PERSON><PERSON> - [abhis<PERSON><PERSON>-anand](https://www.drupal.org/u/abhishek-anand)
