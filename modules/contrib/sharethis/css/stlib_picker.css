.stp_pickerLeft,
.stp_pickerArrow,
.stp_pickerRight {
  display: block;
  float: left;
  height: 254px;
  width: 250px;
  border: 1px solid black;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  overflow: hidden;
}

.stp_pickerArrow {
  width: 92px;
  border: none;
  margin-top: 40px;
}


.stp_header {
  display: block;
  font-size: 1.25em;
  border-bottom: 1px solid black;
  padding: 8px;
  background-color: #115511;
  color: #115511;
  background: #eeeeee;
  background: -moz-linear-gradient(top, #eeeeee 0%, #cccccc 90%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#eeeeee), color-stop(90%,#cccccc));
  filter: "progid:DXImageTransform.Microsoft.gradient( startColorstr='#eeeeee', endColorstr='#cccccc',GradientType=0 )";
}

.stp_ulLeft,
.stp_ulRight {
  display: block;
  overflow-y: scroll;
  height: 215px;
  margin: 0px !important;
  padding: 0px;
  list-style: none;
  background-color: #eeeeee;
}

.stp_li {
  border-bottom: 1px solid #ccc;
  padding: 0px;
  margin: 0px;
  padding-top: 5px;
  background: #ffffee;
  vertical-align: center;
}

.stp_li img {
  display: inline-    block;
  margin: 0px;
  padding: 0px;
  margin-left: 5px;
}

.stp_liText {
  display: inline-block;
  vertical-align: top;
  margin: 0px;
  padding: 5px;
  margin-left: 10px;
  font-size: 1.2em;
  font-family: sans-serif;
  overflow: hidden;
}

.stp_arrow {
  display: block;
  height: 35px;
  width: 35px;
  margin: 30px;
  margin-bottom: 10px;
  margin-top: 10px;
  overflow: hidden;
  text-align: center;
}

.stp_arrow:hover {
  cursor: pointer;
}

.stp_li:hover {
  background: #ddddcc;
}

.stp_select,
.stp_select:hover {
  background: #ccccbb;
}

.stp_clear {
  clear: both;
  width: 500px;
}

.stp_pickerArrow .stp_arrow .stp_up:before {
  content: '\2191';
  font-size: 1.5rem;
}

.stp_pickerArrow .stp_arrow .stp_down:before {
  content: '\2193';
  font-size: 1.5rem;
}

.stp_pickerArrow .stp_arrow .stp_left:before {
  content: '\2190';
  font-size: 1.5rem;
}

.stp_pickerArrow .stp_arrow .stp_right:before {
  content: '\2192';
  font-size: 1.5rem;
}
