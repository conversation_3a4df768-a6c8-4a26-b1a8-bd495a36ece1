# Settings schema.
simplenews.settings:
  type: config_object
  label: 'Simplenews settings'
  mapping:
    hash_expiration:
      type: integer
      label: Hash expiration
    newsletter:
      type: mapping
      label: Newsletter settings
      mapping:
        issue_tokens:
          type: boolean
          label: Show token browser on the node edit page
        format:
          type: string
          label: Default format for new newsletters
        priority:
          type: integer
          label: Default priority for new newsletters
        receipt:
          type: boolean
          label: Default require receipt setting for newsletters
        from_address:
          type: string
          label: Default from email address
        from_name:
          type: label
          label: Default from name

    subscriber:
      type: mapping
      mapping:
        sync_fields:
          type: boolean
          label: Sync between account and subscriber fields

    subscription:
      type: mapping
      label: Subscription settings
      mapping:
        skip_verification:
          type: boolean
          label: Skip verification for anonymous users
        tidy_unconfirmed:
          type: integer
          label: Tidy unconfirmed subscriptions after a number of days
        confirm_subject:
          type: label
          label: Subscribe confirmation subject
        confirm_body:
          type: text
          label: Subscribe confirmation message
        confirm_subscribe_page:
          type: string
          label: Confirmation subscribe redirect page
        confirm_unsubscribe_page:
          type: string
          label: Confirmation unsubscribe redirect page
        validate_subject:
          type: label
          label: Validate subject
        validate_body:
          type: text
          label: Validate message

    mail:
      type: mapping
      label: Mail settings
      mapping:
        use_cron:
          type: boolean
          label: Use cron
        textalt:
          type: boolean
          label: Generate plain-text alternative
        throttle:
          type: integer
          label: Mails to send per cron run
        spool_progress_expiration:
          type: integer
          label: Spool in progress expiration
        spool_expire:
          type: integer
          label: Expiration of sent mails in spool
        debug:
          type: boolean
          label: Debug

# Newsletter config entity schema.
simplenews.newsletter.*:
  type: config_entity
  label: 'Newsletter'
  mapping:
    name:
      type: label
      label: 'Name'
    id:
      type: string
      label: 'Machine-readable name'
    description:
      type: text
      label: 'Description of the newsletter'
    format:
      type: string
      label: 'HTML or plaintext newsletter indicator'
    priority:
      type: integer
      label: 'Priority indicator'
    receipt:
      type: boolean
      label: 'TRUE if a read receipt should be requested.'
    from_name:
      type: label
      label: 'Name of the email author.'
    subject:
      type: label
      label: 'Subject of newsletter email. May contain tokens.'
    from_address:
      type: string
      label: 'Email author address'
    hyperlinks:
      type: boolean
      label: 'Indicates if hyperlinks should be kept inline or extracted.'
    allowed_handlers:
      type: sequence
      label: 'Restricts which recipient handlers are allowed.'
      sequence:
        type: string
        label: Allowed recipient handler
    new_account:
      type: string
      label: 'Indicates how to integrate with the register form.'
    access:
      type: string
      label: 'Controls access to subscribe and unsubscribe.'
    reason:
      type: text
      label: 'Reason why the subscriber is receiving this newsletter.'
    weight:
      type: integer
      label: 'Weight of this newsletter (used for sorting).'

field.simplenews_subscription.settings:
  type: mapping
  label: 'Subscription settings'
  mapping:
    target_type:
      type: string
      label: 'Type of item to reference'

field.simplenews_subscription.instance_settings:
  type: mapping
  label: 'Subscription settings'
  mapping:
    handler:
      type: string
      label: 'Reference method'
    handler_settings:
      type: entity_reference.[%parent.handler].handler_settings
      label: 'Reference method settings'

# Monitoring schema.
monitoring.settings.simplenews_pending:
  type: monitoring.settings_base
  label: 'Simplenews pending sensor settings'
  mapping: { }

# Simplenews issue field type schema.
field.storage_settings.simplenews_issue:
  type: field.storage_settings.entity_reference
  label: 'Simplenews issue settings'
  mapping: { }

field.field_settings.simplenews_issue:
  type: field.field_settings.entity_reference
  label: 'Simplenews issue settings'

field.value.simplenews_issue:
  type: field.value.entity_reference
  label: 'Default value'
  mapping:
    handler:
      type: string
      label: Handler
    handler_settings:
      type: mapping
      label: Handler settings
    status:
      type: integer
      label: Sent status
    sent_count:
      type: integer
      label: Sent count
    error_count:
      type: integer
      label: Error count
    subscribers:
      type: integer
      label: Subscriber count

# Simplenews suscription field type schema.
field.storage_settings.simplenews_subscription:
  type: field.storage_settings.entity_reference
  label: 'Simplenews subscription settings'
  mapping: { }

field.field_settings.simplenews_subscription:
  type: field.field_settings.entity_reference
  label: 'Simplenews subscription settings'

field.value.simplenews_subscription:
  type: field.value.entity_reference
  label: 'Default value'
  mapping: { }

# Block schema
block.settings.simplenews_subscription_block:
  type: block_settings
  mapping:
    newsletters:
      type: sequence
      label: Newsletters
      sequence:
        type: string
        label: Newsletter ID
    default_newsletters:
      type: sequence
      label: Default newsletters
      sequence:
        type: string
        label: Newsletter ID
    message:
      type: label
      label: Message
    show_manage:
      type: boolean
      label: Show manage link
    unique_id:
      type: string
      label: Unique ID

action.configuration.simplenews_send_action:
  type: action_configuration_default
  label: 'Send selected newsletter issue'

action.configuration.simplenews_stop_action:
  type: action_configuration_default
  label: 'Stop selected newsletter issue'
