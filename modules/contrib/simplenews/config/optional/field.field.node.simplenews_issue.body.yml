id: node.simplenews_issue.body
status: true
langcode: en
field_name: body
entity_type: node
bundle: simplenews_issue
label: Body
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  display_summary: false
third_party_settings: {  }
field_type: text_with_summary
dependencies:
  config:
    - field.storage.node.body
    - node.type.simplenews_issue
  module:
    - text
