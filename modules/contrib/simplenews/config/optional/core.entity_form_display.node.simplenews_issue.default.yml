langcode: en
status: true
dependencies:
  config:
    - field.field.node.simplenews_issue.body
    - field.field.node.simplenews_issue.simplenews_issue
    - node.type.simplenews_issue
  module:
    - options
    - text
id: node.simplenews_issue.default
targetEntityType: node
bundle: simplenews_issue
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 2
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 10
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    settings:
      display_label: true
    weight: 15
    third_party_settings: {  }
  simplenews_issue:
    type: simplenews_issue
    weight: 3
    settings: {  }
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    settings:
      display_label: true
    weight: 16
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    settings:
      match_operator: CONTAINS
      size: 60
      placeholder: ''
      match_limit: 10
    third_party_settings: {  }
hidden: {  }
