language: php
cache:
  bundler: true
  directories:
    - $HOME/tmp/drush
    - $HOME/.bundle
  apt: true

php:
  - 5.4
  - 5.5

notifications:
  email:
    - sasch<PERSON><PERSON>@gmail.com
    - <EMAIL>
    - <EMAIL>

env:
  - PATH=$PATH:/home/<USER>/.composer/vendor/bin

# This will create the database
mysql:
  database: drupal
  username: root
  encoding: utf8

# To be able to run a webbrowser
# If we need anything more powerful
# than e.g. phantomjs
before_install:
  - "export DISPLAY=:99.0"
  - "sh -e /etc/init.d/xvfb start"

install:
  # Grab Drush
  - composer global require drush/drush:dev-master --prefer-source
  - cd /home/<USER>/.composer/vendor/drush/drush && cd -
  # Make sure we don't fail when checking out projects
  - echo -e "Host github.com\n\tStrictHostKeyChecking no\n" >> ~/.ssh/config
  # LAMP package installation (mysql is already started)
  - sudo apt-get update
  - sudo apt-get install apache2 libapache2-mod-fastcgi
  # enable php-fpm, travis does not support any other method with php and apache
  - sudo cp ~/.phpenv/versions/$(phpenv version-name)/etc/php-fpm.conf.default ~/.phpenv/versions/$(phpenv version-name)/etc/php-fpm.conf
  - sudo a2enmod rewrite actions fastcgi alias
  - echo "cgi.fix_pathinfo = 1" >> ~/.phpenv/versions/$(phpenv version-name)/etc/php.ini
  - ~/.phpenv/versions/$(phpenv version-name)/sbin/php-fpm
  # Make sure the apache root is in our wanted directory
  - echo "$(curl -fsSL https://gist.githubusercontent.com/nickveenhof/11386315/raw/b8abaf9304fe12b5cc7752d39c29c1edae8ac2e6/gistfile1.txt)" | sed -e "s,PATH,$TRAVIS_BUILD_DIR/../drupal,g" | sudo tee /etc/apache2/sites-available/default > /dev/null
  # Set sendmail so drush doesn't throw an error during site install.
  - echo "sendmail_path='true'" >> `php --ini | grep "Loaded Configuration" | awk '{print $4}'`
  # Forward the errors to the syslog so we can print them
  - echo "error_log=syslog" >> `php --ini | grep "Loaded Configuration" | awk '{print $4}'`
  # Get latest drupal 8 core
  - cd $TRAVIS_BUILD_DIR/..
  - git clone --depth 1 --branch 8.0.x http://git.drupal.org/project/drupal.git
  - cd drupal/modules
  - git clone --depth 1 --branch 8.x-1.x http://git.drupal.org/project/monitoring.git
  # Restart apache and test it
  - sudo service apache2 restart
  - curl -v "http://localhost"
  # Re-enable when trying to get CodeSniffer doesn't return a 403 anymore.
  #- composer global require drupal/coder:\>7

before_script:
  - cd $TRAVIS_BUILD_DIR/../drupal
  # Update drupal core
  - git pull origin 8.0.x
  # Install the site
  - drush -v site-install minimal --db-url=mysql://root:@localhost/drupal --yes
  - drush en --yes simpletest
  - drush cr
  - phpenv rehash

script:
  # go to our Drupal module directory
  - mkdir $TRAVIS_BUILD_DIR/../drupal/modules/simplenews
  - cp -R $TRAVIS_BUILD_DIR/* $TRAVIS_BUILD_DIR/../drupal/modules/simplenews/
  # go to our Drupal main directory
  - cd $TRAVIS_BUILD_DIR/../drupal
  - ls -la $TRAVIS_BUILD_DIR/../drupal/sites/default
  # Run the tests
  - php core/scripts/run-tests.sh --verbose --color --concurrency 4 --php `which php` --url http://localhost "simplenews" | tee /tmp/test.txt; TEST_EXIT=${PIPESTATUS[0]}; echo $TEST_EXIT
  # Check if we had fails in the run-tests.sh script
  # Exit with the inverted value, because if there are no fails found, it will exit with 1 and for us that\
  # is a good thing so invert it to 0. Travis has some issues with the exclamation mark in front so we have to fiddle a
  # bit.
  # Also make the grep case insensitive and fail on run-tests.sh regular fails as well on fatal errors.
  - TEST_OUTPUT=$(! egrep -i "([0-9]+ fails)|(PHP Fatal error)|([0-9]+ exceptions)" /tmp/test.txt > /dev/null)$?
  - echo $TEST_OUTPUT
  - cd $TRAVIS_BUILD_DIR/../drupal/core
  - ./vendor/bin/phpunit --verbose --debug ../modules/simplenews/; TEST_PHPUNIT=$?; echo $TEST_PHPUNIT
  # if the TEST_EXIT status is 0 AND the TEST_OUTPUT status is also 0 it means we succeeded, in all other cases we
  # failed.
  # Re-enable when trying to get CodeSniffer doesn't return a 403 anymore.
  #- /home/<USER>/.composer/vendor/bin/phpcs --standard=/home/<USER>/.composer/vendor/drupal/coder/coder_sniffer/Drupal --extensions=php,inc,test,module,install --ignore=css/ $TRAVIS_BUILD_DIR/../drupal/modules/search_api
  - php -i | grep 'php.ini'
  - sudo cat /var/log/apache2/error.log
  - sudo cat /var/log/syslog | grep 'php'
  # Exit the build
  - if [ $TEST_EXIT -eq 0 ] && [ $TEST_OUTPUT -eq 0 ] && [ $TEST_PHPUNIT -eq 0 ]; then exit 0; else exit 1; fi
