<?php

namespace Drupal\simplenews\RecipientHandler\Annotation;

use <PERSON><PERSON><PERSON>\Component\Annotation\Plugin;

/**
 * Defines an recipient handler annotation object.
 *
 * Plugin Namespace: Plugin\RecipientHandler.
 *
 * For a working example, see
 * \Drupal\simplenews\Plugin\simplenews\RecipientHandler\RecipientHandler
 *
 * @see \Drupal\simplenews\RecipientHandler\RecipientHandlerManager
 * @see \Drupal\simplenews\RecipientHandler\RecipientHandlerInterface
 * @see plugin_api
 *
 * @Annotation
 */
class RecipientHandler extends Plugin {

  /**
   * The archiver plugin ID.
   *
   * @var string
   */
  public $id;

  /**
   * The human-readable name of the recipient handler plugin.
   *
   * @var \Drupal\Core\Annotation\Translation
   *
   * @ingroup plugin_translatable
   */
  public $title;

  /**
   * The description of the recipient handler plugin.
   *
   * @var \Drupal\Core\Annotation\Translation
   *
   * @ingroup plugin_translatable
   */
  public $description;

}
