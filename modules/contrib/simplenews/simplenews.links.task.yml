simplenews.settings_subscriber:
  title: Settings
  description: 'Simplenews subscriber settings.'
  base_route: simplenews.settings_subscriber
  route_name: simplenews.settings_subscriber

simplenews.newsletter_list:
  title: Newsletters
  description: 'Listing of all newsletters.'
  base_route: simplenews.newsletter_list
  route_name: simplenews.newsletter_list

simplenews.settings:
  title: Settings
  description: 'Simplenews settings.'
  base_route: simplenews.newsletter_list
  route_name: simplenews.settings_newsletter

simplenews.settings_newsletter:
  title: Newsletter
  description: 'Simplenews newsletter settings.'
  base_route: simplenews.newsletter_list
  route_name: simplenews.settings_newsletter
  parent_id: simplenews.settings
  weight: -15

simplenews.settings_subscription:
  title: Subscription
  description: 'Subscription settings, opt-in/out confirmation email text.'
  base_route: simplenews.newsletter_list
  route_name: simplenews.settings_subscription
  parent_id: simplenews.settings
  weight: -10

simplenews.settings_mail:
  title: 'Send mail'
  description: 'Send mail, cron and debug options.'
  base_route: simplenews.newsletter_list
  route_name: simplenews.settings_mail
  parent_id: simplenews.settings
  weight: -5

simplenews.settings_prepare_uninstall:
  title: 'Prepare uninstall'
  description: 'Remove fields and data created by Simplenews module.'
  base_route: simplenews.newsletter_list
  route_name: simplenews.settings_prepare_uninstall
  parent_id: simplenews.settings
  weight: -4

simplenews.newsletter_subscriptions_user:
  title: Newsletters
  description: 'Configure your newsletter subscriptions.'
  base_route: entity.user.canonical
  route_name: simplenews.newsletter_subscriptions_user

simplenews.node_tab:
  title: Newsletter
  route_name: simplenews.node_tab
  base_route: entity.node.canonical
  weight: 5

entity.simplenews_subscriber.collection:
  title: 'Subscribers'
  route_name: entity.simplenews_subscriber.collection
  base_route: entity.user.collection
  weight: 15

simplenews.subscribers_sub:
  title: 'List'
  route_name: entity.simplenews_subscriber.collection
  parent_id: entity.simplenews_subscriber.collection

simplenews.subscriber_import:
  title: 'Mass subscribe'
  route_name: simplenews.subscriber_import
  parent_id: entity.simplenews_subscriber.collection

simplenews.subscriber_unsubscribe:
  title: 'Mass unsubscribe'
  route_name: simplenews.subscriber_unsubscribe
  parent_id: entity.simplenews_subscriber.collection

simplenews.subscriber_export:
  title: 'Export'
  route_name: simplenews.subscriber_export
  parent_id: entity.simplenews_subscriber.collection
