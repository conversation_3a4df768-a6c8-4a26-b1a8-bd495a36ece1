# Adapted from https://github.com/Gizra/og/blob/8.x-1.x/.travis.yml
language: php
sudo: false

php:
  - 7.1
  - 7.0
  - 7.2

env:
  - TEST_SUITE=CODE_QUALITY
  - TEST_SUITE=8.4.x
  - TEST_SUITE=8.5.x

# Only run the coding standards check once.
matrix:
  exclude:
    - php: 7.0
      env: TEST_SUITE=CODE_QUALITY
    - php: 7.2
      env: TEST_SUITE=CODE_QUALITY
  allow_failures:
    - php: 7.2

mysql:
  database: schemata
  username: root
  encoding: utf8

before_script:
  # Remove Xdebug as we don't need it and it causes "PHP Fatal error: Maximum
  # function nesting level of '256' reached."
  # We also don't care if that file exists or not on PHP 7.
  - phpenv config-rm xdebug.ini || true

  # Make sure Composer is up to date.
  - composer self-update

  # Remember the current directory for later use in the Drupal installation.
  - MODULE_DIR=$(pwd)

  # Install Composer dependencies for Schemata.
  # @todo not needed for phpunit tests.
  - composer install

  # Navigate out of module directory to prevent blown stack by recursive module
  # lookup.
  - cd ..

  # Create database.
  - mysql -e 'create database schemata'

  # Download Drupal 8 core. Skip this for the coding standards test.
  - test ${TEST_SUITE} == "CODE_QUALITY" || travis_retry git clone --branch $TEST_SUITE --depth 1 https://git.drupal.org/project/drupal.git

  # Remember the Drupal installation path.
  - DRUPAL_DIR=$(pwd)/drupal

  # Install Composer dependencies for core. Skip this for the coding standards test.
  - test ${TEST_SUITE} == "CODE_QUALITY" || composer install --working-dir=$DRUPAL_DIR

  # Add Schemata dependent libraries. They must be installed as part of Drupal for autoloading..
  # This needs to be manually updated whenever composer.json is updated.
  - test ${TEST_SUITE} == "CODE_QUALITY" || composer require --dev league/json-guard:^1.0 league/json-reference:^1.0 --working-dir=$DRUPAL_DIR || true

  # Start a web server on port 8888 in the background.
  - test ${TEST_SUITE} == "CODE_QUALITY" || nohup php -S localhost:8888 --docroot $DRUPAL_DIR > /dev/null 2>&1 &

  # Wait until the web server is responding.
  - test ${TEST_SUITE} == "CODE_QUALITY" || until curl -s localhost:8888; do true; done > /dev/null

  # Export web server URL for browser tests.
  - export SIMPLETEST_BASE_URL=http://localhost:8888

  # Export database variable for kernel tests.
  - export SIMPLETEST_DB=mysql://root:@127.0.0.1/schemata

script: DRUPAL_DIR=$DRUPAL_DIR MODULE_DIR=$MODULE_DIR $MODULE_DIR/bin/travis-run-test.sh $TEST_SUITE
