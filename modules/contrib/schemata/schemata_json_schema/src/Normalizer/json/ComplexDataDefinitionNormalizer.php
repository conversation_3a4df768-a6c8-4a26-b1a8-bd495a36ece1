<?php

namespace Drupal\schemata_json_schema\Normalizer\json;

use Dr<PERSON>al\Core\TypedData\ComplexDataDefinitionInterface;
use <PERSON><PERSON>al\Core\TypedData\DataReferenceTargetDefinition;
use <PERSON><PERSON>al\Component\Utility\NestedArray;

/**
 * Normalizer for ComplexDataDefinitionInterface.
 *
 * ComplexDataDefinitions represent objects - compound values whose objects
 * have string keys. Almost all fields are complex in this way, with their key
 * data stored in an object property of "value". In turn, these objects are
 * wrapped in an array which is normalized by ListDataDefinitionNormalizer.
 */
class ComplexDataDefinitionNormalizer extends DataDefinitionNormalizer {

  /**
   * The interface or class that this Normalizer supports.
   *
   * @var string
   */
  protected $supportedInterfaceOrClass = ComplexDataDefinitionInterface::class;

  /**
   * {@inheritdoc}
   */
  public function normalize($entity, $format = NULL, array $context = []): array|bool|string|int|float|null|\ArrayObject {
    /** @var \Drupal\Core\TypedData\ComplexDataDefinitionInterface $entity */
    $context['parent'] = $entity;
    $normalized = $this->extractPropertyData($entity);
    $normalized['type'] = 'object';

    // Retrieve 'properties' and possibly 'required' nested arrays.
    $properties = $this->normalizeProperties(
      $entity->getPropertyDefinitions(),
      $format,
      $context
    );

    $normalized = NestedArray::mergeDeep($normalized, $properties);
    return $normalized;
  }

  /**
   * Determine if the current field is a reference field.
   *
   * @param \Drupal\Core\TypedData\ComplexDataDefinitionInterface $entity
   *   The complex data definition to be checked.
   * @param array $context
   *   The current serializer context.
   *
   * @return bool
   *   TRUE if it is a reference, FALSE otherwise.
   */
  protected function isReferenceField(ComplexDataDefinitionInterface $entity, array $context = NULL) {
    $main = $entity->getPropertyDefinition($entity->getMainPropertyName());
    // @todo use an interface or API call instead of an object check.
    return ($main instanceof DataReferenceTargetDefinition);
  }

  /**
   *{@inheritdoc}
   */
  public function getSupportedTypes(?string $format): array {
    return [
      ComplexDataDefinitionInterface::class => true,
    ];
  }

}
