<?php

namespace Drupal\schemata_json_schema\Normalizer\json;

use Dr<PERSON>al\Core\TypedData\ComplexDataDefinitionInterface;
use <PERSON><PERSON>al\Core\TypedData\DataReferenceTargetDefinition;
use <PERSON><PERSON>al\Core\TypedData\ListDataDefinitionInterface;

/**
 * Normalizer for ListDataDefinitionInterface objects.
 *
 * Almost all entity properties in the system are a list of values, each value
 * in the "List" might be a ComplexDataDefinitionInterface (an object) or it
 * might be more of a scalar.
 */
class ListDataDefinitionNormalizer extends DataDefinitionNormalizer {

  /**
   * The interface or class that this Normalizer supports.
   *
   * @var string
   */
  protected $supportedInterfaceOrClass = ListDataDefinitionInterface::class;

  /**
   * {@inheritdoc}
   */
  public function normalize($entity, $format = NULL, array $context = []): array|bool|string|int|float|null|\ArrayObject {
    /** @var \Drupal\Core\TypedData\ListDataDefinitionInterface $entity */
    $context['parent'] = $entity;
    $property = $this->extractPropertyData($entity, $context);
    $property['type'] = 'array';

    // This retrieves the definition common to ever item in the list, and
    // serializes it so we can define how members of the array should look.
    // There are no lists that might contain items of different types.
    $property['items'] = $this->serializer->normalize(
      $entity->getItemDefinition(),
      $format,
      $context
    );

    // FieldDefinitionInterface::isRequired() explicitly indicates there must be
    // at least one item in the list. Extending this reasoning, the same must be
    // true of all ListDataDefinitions.
    if ($this->requiredProperty($entity)) {
      $property['minItems'] = 1;
    }

    $normalized = ['properties' => []];
    $normalized['properties'][$context['name']] = $property;
    if ($this->requiredProperty($entity)) {
      $normalized['required'][] = $context['name'];
    }

    return $normalized;
  }

  /**
   * Determine if the current field is a reference field.
   *
   * @param \Drupal\Core\TypedData\ListDataDefinitionInterface $entity
   *   The list definition to be checked.
   *
   * @return bool
   *   TRUE if it is a reference, FALSE otherwise.
   */
  protected function isReferenceField(ListDataDefinitionInterface $entity) {
    $item = $entity->getItemDefinition();
    if ($item instanceof ComplexDataDefinitionInterface) {
      $main = $item->getPropertyDefinition($item->getMainPropertyName());
      // @todo use an interface or API call instead of an object check.
      return ($main instanceof DataReferenceTargetDefinition);
    }

    return FALSE;
  }

  /**
   *{@inheritdoc}
   */
  public function getSupportedTypes(?string $format): array {
    return [
      ListDataDefinitionInterface::class => true,
    ];
  }

}
