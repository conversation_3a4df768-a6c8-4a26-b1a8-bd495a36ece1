{"name": "drupal/schemata", "description": "Facilitate generation of schema definitions of Drupal 8 data models.", "type": "drupal-module", "keywords": ["<PERSON><PERSON><PERSON>", "json", "schema"], "homepage": "https://drupal.org/project/schemata", "support": {"issues": "https://drupal.org/project/issues/schemata", "source": "https://cgit.drupalcode.org/schemata"}, "license": "GPL-2.0-or-later", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "prefer-stable": true, "minimum-stability": "dev", "config": {"sort-packages": true}, "scripts": {"phpcs": "phpcs --standard=tests/phpcs.xml", "phpcbf": "phpcbf --standard=tests/phpcs.xml", "lint": "parallel-lint -e php,module,install,profile,theme,inc --exclude vendor/ --blame .", "quality": ["@lint", "@phpcs"]}, "require": {"php": ">=8.1"}, "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8"}], "require-dev": {"drupal/coder": "^8.2", "drupal/hal": "^1 || ^2", "jakub-onderka/php-parallel-lint": "^0.9.2", "justinrainbow/json-schema": "^5.2"}}