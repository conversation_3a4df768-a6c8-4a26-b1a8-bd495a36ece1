<?php

use <PERSON><PERSON><PERSON>\Core\Routing\RouteMatchInterface;
use S<PERSON>fony\Component\HttpFoundation\RedirectResponse;
use <PERSON><PERSON>al\user\UserInterface;
use Drupal\user\Entity\User;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Url;
use Drupal\commerce_order\Entity\OrderInterface;
use Drupal\Core\StringTranslation\TranslatableMarkup;
use Drupal\Core\Mail\MailManagerInterface;
use Symfony\Component\Mailer\Mailer;
use Symfony\Component\Mailer\Transport\Smtp\EsmtpTransport;
use Symfony\Component\Mailer\Transport\Smtp\EsmtpTransportInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mime\Part\TextPart;
use Symfony\Component\Mime\Part\DataPart;


/**
 * Implements hook_commerce_order_update().
 *
 * This hook is invoked after an order is updated, allowing you to act upon it
 * after the update.
 */
// function role_change_on_checkout_commerce_order_update(OrderInterface $order) {
//   // Check if the order status has changed to completed.
//   if ($order->hasField('state') && $order->get('state')->value == 'completed') {
//     $user_id = $order->getCustomerId();
//     if ($user_id) {
//       $user = User::load($user_id);


// if ($user && contains_product_id($order, 5)) {
//       foreach ($order->getItems() as $item) {
//         $product = $item->getPurchasedEntity();
//         if ($product && $product->getProductId() == 5) {
//           \Drupal::logger('role_change_on_checkout')->notice('Product ID 2 found in order @order_id', ['@order_id' => $order->id()]);
          
//           // Send the DOC via email to the customer.
//           send_creator_role_notification_email($user, $order);
//             // If you only want to send one email per order, break after finding the product.
//         }
//   }

//       // if ($user && contains_product_id($order, 4)) {
//       //   // Add the role if not already assigned.
//       //   if (!$user->hasRole('creator')) {
//       //     $user->addRole('creator');
//       //     $user->save();
//       //     \Drupal::logger('role_change_on_checkout')->info('Added creator role to user @uid upon order completion.', ['@uid' => $user->id()]);

//       //     // Send an email notification to the user about the role change.
//       //     send_creator_role_notification($user, $order);
//       //   }

//         // Update the custom role field with suffix.
//         $custom_role_field = $user->get('field_custom_role')->value;
//         if ($custom_role_field) {
//           if (substr($custom_role_field, -2) !== '-m') {
//             $user->set('field_custom_role', $custom_role_field . '-m');
//           }
//         } else {
//           $user->set('field_custom_role', 'creator'); // Initialize with role and suffix if field is empty.
//         }

//         $user->save();
//         \Drupal::logger('role_change_on_checkout')->info('Updated field_custom_role for user @uid to @value.', ['@uid' => $user->id(), '@value' => $user->get('field_custom_role')->value]);
//       }
//     }
//   }
// }

// /**
//  * Checks if an order contains a product with the given product ID.
//  *
//  * @param \Drupal\commerce_order\Entity\OrderInterface $order
//  *   The order entity.
//  * @param int $product_id
//  *   The product ID to check for.
//  *
//  * @return bool
//  *   TRUE if the product ID is found in the order, FALSE otherwise.
//  */
// function contains_product_id(OrderInterface $order, $product_id) {
//   foreach ($order->getItems() as $order_item) {
//     $purchased_entity = $order_item->getPurchasedEntity();
//     if ($purchased_entity && $purchased_entity->getProductId() == $product_id) {
//       return TRUE;
//     }
//   }
//   return FALSE;
// }

/**
 * Sends an email notification to the user when they are assigned the creator role.
 *
 * @param \Drupal\user\Entity\User $user
 *   The user entity.
 * @param \Drupal\commerce_order\Entity\OrderInterface $order
 *   The order entity.
 */
function send_creator_role_notification_email(User $user, OrderInterface $order) {
  // Create the transport (use your SMTP server details)
  $transport = new EsmtpTransport('smtp.gmail.com', 587);
  $transport->setUsername('<EMAIL>')
            ->setPassword('guvoyouixdjdwwiu');
  
  // Create the Mailer
  $mailer = new Mailer($transport);

  $file_path = 'sites/default/files/notice.doc'; // Ensure this path is correct

  // Check if the file exists
  if (file_exists($file_path)) {
    $file_content = file_get_contents($file_path);
    $file_name = 'notice.doc';

    // Create the email message
    $email = (new Email())
      ->from('<EMAIL>')
      ->to($user->getEmail())
      ->subject('You have a DOC file to download')
      ->html('Congratulations ' . $user->getDisplayName() . ', you have been assigned the creator role on our website. Please find the attached welcome guide.');

    // Attach the DOC file
    $attachment = new DataPart($file_content, $file_name, 'application/msword');
    $email->attachPart($attachment);

    // Send the email
    try {
      $mailer->send($email);
      \Drupal::logger('product_pdf_access')->notice('Successfully sent DOC file to user @uid.', ['@uid' => $user->id()]);
    } catch (\Exception $e) {
      \Drupal::logger('product_pdf_access')->error('Failed to send DOC file to user @uid. Error: @error', ['@uid' => $user->id(), '@error' => $e->getMessage()]);
    }
  } else {
    \Drupal::logger('product_pdf_access')->error('DOC file not found at path: @path', ['@path' => $file_path]);
  }
}

/**
 * Implements hook_user_role_insert().
 *
 * This hook is invoked when a role is added to a user.
 */
// function role_change_on_checkout_user_role_insert(User $account, Role $role) {
//   if ($role->id() == 'creator') {
//     $custom_role_field = $account->get('field_custom_role')->value;
//     if ($custom_role_field) {
//       if (substr($custom_role_field, -2) !== '-c') {
//         $account->set('field_custom_role', $custom_role_field . '-c');
//       }
//     } else {
//       $account->set('field_custom_role', 'creator'); // Initialize with role and suffix if field is empty.
//     }
//     $account->save();
//     \Drupal::logger('role_change_on_checkout')->info('Updated field_custom_role for user @uid to @value on role addition.', ['@uid' => $account->id(), '@value' => $account->get('field_custom_role')->value]);
//   }
// }

/**
 * Implements hook_help().
 */
function role_change_on_checkout_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.role_change_on_checkout':
      return '<p>' . t('This module changes the user role and updates custom role field when a checkout process is completed.') . '</p>';
  }
}
