<?php

namespace <PERSON><PERSON>al\role_change_on_checkout\EventSubscriber;

use <PERSON>ymfony\Component\EventDispatcher\EventSubscriberInterface;
use Drupal\commerce_order\Event\OrderEvent;
use Drupal\commerce_order\Event\OrderEvents;
use <PERSON><PERSON>al\user\Entity\User;
use <PERSON>upal\Core\Logger\LoggerChannelFactoryInterface;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Listens to the order paid event to change user roles.
 */
class CheckoutPaidSubscriber implements EventSubscriberInterface {

  /**
   * The logger factory.
   *
   * @var \Drupal\Core\Logger\LoggerChannelFactoryInterface
   */
  protected $loggerFactory;

  /**
   * Constructs a CheckoutPaidSubscriber object.
   *
   * @param \Drupal\Core\Logger\LoggerChannelFactoryInterface $logger_factory
   *   The logger factory.
   */
  public function __construct(LoggerChannelFactoryInterface $logger_factory) {
    $this->loggerFactory = $logger_factory;
  }

  /**
   * Responds to order paid events.
   *
   * @param \Drupal\commerce_order\Event\OrderEvent $event
   *   The order event.
   */
  public function onOrderPaid(OrderEvent $event) {
    $order = $event->getOrder();

    // Ensure the order has a customer reference.
    $user_id = $order->getCustomerId();
    if ($user_id) {
      $user = User::load($user_id);

      if ($user && !$user->hasRole('member')) {
        $user->addRole('member');
        $this->loggerFactory->get('role_change_on_checkout')->info('Added new_role to user @uid upon order payment.', ['@uid' => $user->id()]);
        $user->save();
      }
    }
  }

  /**
   * {@inheritdoc}
   */
  public static function getSubscribedEvents() {
    // Respond to the order paid event.
    $events[OrderEvents::ORDER_PAID][] = ['onOrderPaid'];
    return $events;
  }
}
