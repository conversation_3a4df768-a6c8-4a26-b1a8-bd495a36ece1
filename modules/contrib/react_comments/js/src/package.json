{"name": "react-comments", "version": "0.1.0", "private": true, "dependencies": {"axios": "^0.16.2", "cookie": "^0.3.1", "draft-js": "^0.11.0", "react": "^15.6.0", "react-dom": "^15.6.0", "react-redux": "^5.0.5", "react-throbber": "^1.0.4", "redux": "^3.6.0", "redux-thunk": "^2.2.0", "timeago.js": "^3.0.2"}, "devDependencies": {"react-scripts": "1.0.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --env=jsdom", "eject": "react-scripts eject"}}