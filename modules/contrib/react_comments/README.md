INTRODUCTION
------------

The React Comments module acts as a drop-in replacement for the Drupal core
comment module front end.

 * For a full description of the module visit:
   https://www.drupal.org/project/react_comments

 * To submit bug reports and feature suggestions, or to track changes visit:
   https://www.drupal.org/project/issues/react_comments


REQUIREMENTS
------------

This module requires no modules outside of Drupal core.


INSTALLATION
------------

 * Install the React Comments module as you would normally install a contributed
   Drupal module. Visit https://www.drupal.org/node/1897420 for further
   information.


CONFIGURATION
-------------

 * Navigate to Administration > Extend and enable the module.

No configuration is necessary. Upon installation, comment fields will be
replaced by an interactive React app that allows user actions
(commenting/replying/editing etc) without requiring a full page refresh or
navigation away from the current page. Other benefits include the ability to
administer comments directly from the front end (users with the 'administer
comments' permission will see all unpublished comments and be able to perform
administrative tasks -- publish/unpublish/delete -- directly via the front end),
a clean (and familiar) UI, and other minor UX improvements such as reverse
chronological sorting, explicit "reply to", and built in flagging.


MAINTAINERS
-----------

 * Minnur Yunusov (minnur) - https://www.drupal.org/u/minnur
 * Theo Ballew (void--) - https://www.drupal.org/u/void

Supporting organization:

 *  Chapter Three - https://www.drupal.org/chapter-three
