<?php

namespace Drupal\redirect_login\EventSubscriber;

use <PERSON>upal\Core\Session\AccountInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * Redirects user based on role after login.
 */
class UserLoginRedirectSubscriber implements EventSubscriberInterface {

  /**
   * The current user service.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected $currentUser;

  /**
   * The request stack service.
   *
   * @var \Symfony\Component\HttpFoundation\RequestStack
   */
  protected $requestStack;

  /**
   * Constructs a UserLoginRedirectSubscriber object.
   *
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user service.
   * @param \Symfony\Component\HttpFoundation\RequestStack $request_stack
   *   The request stack service.
   */
  public function __construct(AccountInterface $current_user, RequestStack $request_stack) {
    $this->currentUser = $current_user;
    $this->requestStack = $request_stack;
  }

  /**
   * {@inheritdoc}
   */
  public static function getSubscribedEvents() {
    // Returns an array of events and their corresponding methods.
    $events[KernelEvents::REQUEST][] = ['onRequest', 10];
    return $events;
  }

  /**
   * Redirects the user after login.
   *
   * @param \Symfony\Component\HttpKernel\Event\RequestEvent $event
   *   The request event.
   */
  public function onRequest(RequestEvent $event) {
    $request = $event->getRequest();
    $path = $request->getPathInfo();

    // Check if the current request is for login.
    if ($path === '/user/login' && $this->currentUser->isAuthenticated()) {
      // Redirect to a specific path based on user roles.
      if ($this->currentUser->hasRole('creator') || $this->currentUser->hasRole('participants')) {
        $response = new RedirectResponse('/list-creabook');
        $event->setResponse($response);
      }
    }
  }
}
