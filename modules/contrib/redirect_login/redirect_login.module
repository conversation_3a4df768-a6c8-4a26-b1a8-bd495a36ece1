<?php

use <PERSON><PERSON>al\Core\Form\FormStateInterface;

/**
 * Implements hook_form_alter().
 */
function login_redirect_form_user_login_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  // Ensure that we are targeting the correct form.
  if ($form_id === 'user_login_form') {
    // Add a submit handler to redirect after login.
    $form['#submit'][] = 'login_redirect_user_login_form_submit';
  }
}

/**
 * Custom submit handler for the user login form.
 */
function login_redirect_user_login_form_submit(array &$form, FormStateInterface $form_state) {
  // Redirect users after login based on their roles.
  $user = $form_state->getUser();

  if ($user->isAuthenticated()) {
    if ($user->hasRole('creator') || $user->hasRole('participants')) {
      // Add a redirect URL to the form state.
      $form_state->setRedirect('list_creabook');
    }
  }
}
