opentimestamp.admin:
  title: 'OpenTimestamps'
  description: 'Interface pour horodater des fichiers via OpenTimestamps.'
  route_name: opentimestamp.form
  parent: system.admin_content
  weight: 100

opentimestamp.admin_manual_form:
  title: 'Horodatage manuel'
  description: 'Horodater manuellement un contenu ou un fichier.'
  route_name: opentimestamp.admin_manual_form
  parent: opentimestamp.admin
  weight: 10

opentimestamp.history:
  title: 'Historique des horodatages'
  description: 'Consulter l''historique des fichiers horodatés.'
  route_name: opentimestamp.history
  parent: opentimestamp.admin
  weight: 20

opentimestamp.settings:
  title: 'OpenTimestamp Settings'
  description: 'Configure OpenTimestamp settings.'
  parent: system.admin_config_system
  route_name: opentimestamp.settings
  weight: 100


# opentimestamp.links.menu.yml

opentimestamp.user_history:
  title: 'Mes attestations'
  description: 'Consulter l’historique de mes horodatages'
  route_name: opentimestamp.history
  parent: user.page
  menu_name: user-menu
  weight: 10

