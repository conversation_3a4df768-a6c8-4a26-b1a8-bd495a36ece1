(function () {
  'use strict';

  const glossary = {
    "CreaBOOK": "Book in which the rights holder describes, proves and claims authorship of a creation.",
    "NFT": "Non-Fungible Token that materializes the ownership of a CreaBOOK.",
    "CreaSAFE": "Encrypted NDA based on CreaBOOK annexes.",
    "WcS": "World creators Society",
    "CreaPOLE": "City or valley managing global creative IP."
  };

  Drupal.behaviors.glossaryTooltip = {
    attach: function (context, settings) {
      const helpTexts = context.querySelectorAll('.description, .help, .form-help');

      helpTexts.forEach(help => {
        const strongs = help.querySelectorAll("strong");
        strongs.forEach(strong => {
          const term = strong.textContent.trim();
          if (glossary[term] && !strong.classList.contains('glossary-added')) {
            const icon = document.createElement("span");
            icon.className = "glossary-icon";
            icon.setAttribute("title", glossary[term]);
            icon.textContent = " ⓘ";
            strong.insertAdjacentElement("afterend", icon);
            strong.classList.add('glossary-added');
          }
        });
      });
    }
  };
})();
