#!/bin/bash

PROJECT_DIR=${1:-"."}

echo "🔎 Scanning for 'entity:user' context issues in Drupal project: $PROJECT_DIR"
echo "----------------------------------------------------------------------"

# Step 1: Routes requiring entity:user
echo ""
echo "📁 [1] ROUTES with 'entity:user' context:"
grep -r --color=always "'entity:user'" "$PROJECT_DIR"/*.routing.yml "$PROJECT_DIR"/modules/custom/ 2>/dev/null

# Step 2: MENU LINKS pointing to those routes
echo ""
echo "🔗 [2] MENU LINKS pointing to affected routes:"
ROUTES=$(grep -r "'entity:user'" "$PROJECT_DIR"/*.routing.yml "$PROJECT_DIR"/modules/custom/ 2>/dev/null | cut -d: -f1 | xargs grep '^  ' | awk '{print $1}' | sed 's/://g')

for route in $ROUTES; do
  grep --color=always -r "$route" "$PROJECT_DIR"/*.links.menu.yml "$PROJECT_DIR"/modules/custom/ 2>/dev/null
done

# Step 3: PHP plugins requiring entity:user
echo ""
echo "🧩 [3] PHP files requiring 'entity:user' in plugins (block, condition, access, etc.):"
grep -r --color=always "'entity:user'" "$PROJECT_DIR"/modules/custom/ "$PROJECT_DIR"/src/ 2>/dev/null | grep -Ei '\.php'

# Step 4: Menu blocks rendered (SystemMenuBlock)
echo ""
echo "📦 [4] BLOCKS rendering menus (could trigger route access checks):"
grep -r "plugin: system_menu_block" "$PROJECT_DIR"/config/ 2>/dev/null | grep "id:"

# Optional: Show enabled blocks from database using Drush (if available)
if command -v drush &> /dev/null; then
  echo ""
  echo "🧱 [5] Active menu blocks (via Drush):"
  drush config:get --include-overridden block.block.main | grep plugin
else
  echo ""
  echo "💡 Tip: Install Drush to list active blocks using: drush config:get block.block.main"
fi

echo ""
echo "✅ Scan complete. Look at routes + blocks pointing to them. Either:"
echo " - Ensure 'entity:user' context is passed properly"
echo " - OR avoid using these routes in anonymous areas like menus or unauthenticated blocks"
