<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* themes/contrib/bootstrap5/templates/content/page--front.html.twig */
class __TwigTemplate_f164c8344115e0c48d9b8f933ff4623e extends Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->env->getExtension('\Twig\Extension\SandboxExtension');
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 46
        echo "
";
        // line 48
        echo "<script type=\"application/ld+json\">
{
  \"@context\": \"https://schema.org\",
  \"@type\": \"FAQPage\",
  \"mainEntity\": [
    {
      \"@type\": \"Question\",
      \"name\": \"What is a CreaBOOK?\",
      \"acceptedAnswer\": {
        \"@type\": \"Answer\",
        \"text\": \"A CreaBOOK is a book that proves who the true author of the described and claimed creation is.\"
      }
    },
    {
      \"@type\": \"Question\",
      \"name\": \"What is the World creators Society (WcS) Organization?\",
      \"acceptedAnswer\": {
        \"@type\": \"Answer\",
        \"text\": \"WcS is a non-governmental organization (NGO) composed of creators that manages the CreaFREE Ecosystem to implement the USIP.\"
      }
    },
    {
      \"@type\": \"Question\",
      \"name\": \"How does CreaPAX work?\",
      \"acceptedAnswer\": {
        \"@type\": \"Answer\",
        \"text\": \"Protecting intellectual property in court requires complex, lengthy, risky and costly national litigation. The World creations' Society (WcS) has developed a streamlined procedure called CreaPAX. It prepares and supports a simple, quick and cost-effective settlement based on good faith. In the event of a refusal by the alleged CreaBOOK infringer, the WcS-appointed expert advocates fast track national trials.\"
      }
    }
  ]
}
</script>

";
        // line 82
        echo "<script type=\"application/ld+json\">
{
  \"@context\": \"https://schema.org\",
  \"@type\": \"WebPage\",
  \"name\": \"CreaFREE Ecosystem\",
  \"description\": \"CreaFREE provides tools for startups to prove authorship of their innovations with ethical progress at its core.Startups can prove
authorship of their innovations \",
  \"mainEntity\": {
    \"@type\": \"Organization\",
    \"name\": \"World creators Society\",
    \"url\": \"https://creafree.org\"
  },
  \"specialty\": [
    \"Innovation Protection\",
    \"Ethical Progress\",
    \"Startup Empowerment\"
  ]
}
</script>";
        // line 101
        $context["nav_classes"] = ((("navbar navbar-expand-lg" . (((        // line 102
($context["b5_navbar_schema"] ?? null) != "none")) ? ((" navbar-" . $this->sandbox->ensureToStringAllowed(($context["b5_navbar_schema"] ?? null), 102, $this->source))) : (" "))) . (((        // line 103
($context["b5_navbar_schema"] ?? null) != "none")) ? ((((($context["b5_navbar_schema"] ?? null) == "dark")) ? (" text-light") : (" text-dark"))) : (" "))) . (((        // line 104
($context["b5_navbar_bg_schema"] ?? null) != "none")) ? ((" bg-" . $this->sandbox->ensureToStringAllowed(($context["b5_navbar_bg_schema"] ?? null), 104, $this->source))) : (" ")));
        // line 106
        echo "
";
        // line 108
        $context["footer_classes"] = (((" " . (((        // line 109
($context["b5_footer_schema"] ?? null) != "none")) ? ((" footer-" . $this->sandbox->ensureToStringAllowed(($context["b5_footer_schema"] ?? null), 109, $this->source))) : (" "))) . (((        // line 110
($context["b5_footer_schema"] ?? null) != "none")) ? ((((($context["b5_footer_schema"] ?? null) == "dark")) ? (" text-light") : (" text-dark"))) : (" "))) . (((        // line 111
($context["b5_footer_bg_schema"] ?? null) != "none")) ? ((" bg-" . $this->sandbox->ensureToStringAllowed(($context["b5_footer_bg_schema"] ?? null), 111, $this->source))) : (" ")));
        // line 113
        echo "
<header>
  ";
        // line 115
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "header", [], "any", false, false, true, 115), 115, $this->source), "html", null, true);
        echo "

  ";
        // line 117
        if (((twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "nav_branding", [], "any", false, false, true, 117) || twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "nav_main", [], "any", false, false, true, 117)) || twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "nav_additional", [], "any", false, false, true, 117))) {
            // line 118
            echo "  <nav class=\"";
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["nav_classes"] ?? null), 118, $this->source), "html", null, true);
            echo "\">
    <div class=\"";
            // line 119
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["b5_top_container"] ?? null), 119, $this->source), "html", null, true);
            echo " d-flex align-items-center\">
      ";
            // line 121
            echo "      ";
            if (($context["logo"] ?? null)) {
                // line 122
                echo "        <a href=\"";
                echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar($this->extensions['Drupal\Core\Template\TwigExtension']->getPath("<front>"));
                echo "\" title=\"";
                echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Home"));
                echo "\" rel=\"home\" class=\"navbar-brand me-auto\">
          <img src=\"";
                // line 123
                echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["logo"] ?? null), 123, $this->source), "html", null, true);
                echo "\" alt=\"";
                echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Home"));
                echo "\" class=\"img-fluid\" />
        </a>
      ";
            }
            // line 126
            echo "

      <nav class=\"navbar\">
  <div class=\"container\">
    <a class=\"navbar-brand\" href=\"https://creafree.org\">
      <img src=\"https://creafree.org/logov2.png\" alt=\"Bootstrap\" width=\"30\" height=\"24\">
    </a>
  </div>
</nav>


      ";
            // line 137
            if ((twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "nav_main", [], "any", false, false, true, 137) || twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "nav_additional", [], "any", false, false, true, 137))) {
                // line 138
                echo "        <button class=\"navbar-toggler collapsed\" type=\"button\" data-bs-toggle=\"collapse\"
                data-bs-target=\"#navbarSupportedContent\" aria-controls=\"navbarSupportedContent\"
                aria-expanded=\"false\" aria-label=\"Toggle navigation\">
          <span class=\"navbar-toggler-icon\"></span>
        </button>

        <div class=\"collapse navbar-collapse justify-content-md-end\" id=\"navbarSupportedContent\">
          ";
                // line 145
                echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "nav_main", [], "any", false, false, true, 145), 145, $this->source), "html", null, true);
                echo "
          ";
                // line 146
                echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "nav_additional", [], "any", false, false, true, 146), 146, $this->source), "html", null, true);
                echo "
        </div>
      ";
            }
            // line 149
            echo "    </div>
  </nav>
  ";
        }
        // line 152
        echo "
</header>


<main role=\"main\">
  <a id=\"main-content\" tabindex=\"-1\"></a>";
        // line 158
        echo "
  ";
        // line 160
        $context["sidebar_first_classes"] = (((twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_first", [], "any", false, false, true, 160) && twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_second", [], "any", false, false, true, 160))) ? ("col-12 col-sm-6 col-lg-3") : ("col-12 col-lg-3"));
        // line 162
        echo "
  ";
        // line 164
        $context["sidebar_second_classes"] = (((twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_first", [], "any", false, false, true, 164) && twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_second", [], "any", false, false, true, 164))) ? ("col-12 col-sm-6 col-lg-3") : ("col-12 col-lg-3"));
        // line 166
        echo "
  ";
        // line 168
        $context["content_classes"] = (((twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_first", [], "any", false, false, true, 168) && twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_second", [], "any", false, false, true, 168))) ? ("col-12 col-lg-6") : ((((twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_first", [], "any", false, false, true, 168) || twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_second", [], "any", false, false, true, 168))) ? ("col-12 col-lg-9") : ("col-12"))));
        // line 170
        echo "

  <div class=\"";
        // line 172
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["b5_top_container"] ?? null), 172, $this->source), "html", null, true);
        echo "\">
    ";
        // line 173
        if (twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "breadcrumb", [], "any", false, false, true, 173)) {
            // line 174
            echo "      ";
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "breadcrumb", [], "any", false, false, true, 174), 174, $this->source), "html", null, true);
            echo "
    ";
        }
        // line 176
        echo "    <div class=\"row g-0\">
      ";
        // line 177
        if (twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_first", [], "any", false, false, true, 177)) {
            // line 178
            echo "        <div class=\"order-2 order-lg-1 ";
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["sidebar_first_classes"] ?? null), 178, $this->source), "html", null, true);
            echo "\">
          ";
            // line 179
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_first", [], "any", false, false, true, 179), 179, $this->source), "html", null, true);
            echo "
        </div>
      ";
        }
        // line 182
        echo "      <div class=\"order-1 order-lg-2 ";
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["content_classes"] ?? null), 182, $this->source), "html", null, true);
        echo "\">
        ";
        // line 183
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "content", [], "any", false, false, true, 183), 183, $this->source), "html", null, true);
        echo "
       
<!-- Secure your return -->

<div class=\"container-fluid m-0 text-dark\">
    <div class=\"row\">
        <!-- 1/3 Left Section for CreaMAKER Download and Description -->
        <div class=\"col-md-4 col-sm-12 pt-5 fondbleu\">
            <h1 class=\"fs-1 text-start text-light fw-bold pt-3\">
                <svg class=\"bi bi-square-fill\" xmlns=\"http://www.w3.org/2000/svg\" width=\"30\" height=\"30\" fill=\"#f29432\" viewBox=\"0 0 16 16\">
                    <path d=\"M0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2z\"></path>
                </svg>
                Empowerment
            </h1>
            <p class=\"text-light fs-4 text-start mx-3 p-4 pt-2\">
                Startups can prove <br>
authorship <br> of their innovations
            </p>
        </div>
        <!-- 2/3 Right Section with Lists and Text Block -->
        <div class=\"col-md-8 col-sm-12 pt-5\">
            <div class=\"container overflow-hidden text-start\">
                <div class=\"row gx-5\">
                    <!-- Problem Section -->
                    <div class=\"col\">
                        <div class=\"p-3\">
                            <h4 class=\"text-center fw-bold text-danger pb-3\">Problem</h4>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-x-circle-fill text-danger\"></i>
                                <span class=\"ms-2\">90% of innovations from startups do not reach the market</span>
                            </p>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-x-circle-fill text-danger\"></i>
                                <span class=\"ms-2\">Patents are too expensive for worldwide protection</span>
                            </p>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-x-circle-fill text-danger\"></i>
                                <span class=\"ms-2\">Many of the proclaimed inventors are not the true inventors</span>
                            </p>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-x-circle-fill text-danger\"></i>
                                <span class=\"ms-2\">No investment can be made without a strong guarantee</span>
                            </p>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-x-circle-fill text-danger\"></i>
                                <span class=\"ms-2\">Intellectual property rights are not accessible to small companies</span>
                            </p>
                        </div>
                    </div>
                    <!-- Solution Section -->
                    <div class=\"col\">
                        <div class=\"p-3\">
                            <h4 class=\"text-center fw-bold text-success pb-3\">Solution</h4>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-check-circle-fill text-success\"></i>
                                <span class=\"ms-2\">The success rate is increased by the protection of the intrinsic value of innovations</span>
                            </p>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-check-circle-fill text-success\"></i>
                                <span class=\"ms-2\">CreaBOOK offers free and immediate protection based on human rights</span>
                            </p>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-check-circle-fill text-success\"></i>
                                <span class=\"ms-2\">CreaBOOK associates high returns on investment based on genuine authorship</span>
                            </p>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-check-circle-fill text-success\"></i>
                                <span class=\"ms-2\">It is supported by an amicable settlement procedure based on good faith</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


      
<hr>

<!-- Ethics -->

<div class=\"container-fluid m-0 text-dark\">
    <div class=\"row\">
        <!-- 1/3 Left Section for CreaMAKER Download and Description -->
        <div class=\"col-md-4 col-sm-12 pt-5 fondbleu\">
            <h1 class=\"fs-1 text-start text-light fw-bold pt-3\">
                <svg class=\"bi bi-square-fill\" xmlns=\"http://www.w3.org/2000/svg\" width=\"30\" height=\"30\" fill=\"#f29432\" viewBox=\"0 0 16 16\">
                    <path d=\"M0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2z\"></path>
                </svg>
                Ethics
            </h1>
            <p class=\"text-light fs-4 text-start mx-3 p-4 pt-2\">
                Ethical progress<br>
                is essential to master<br>
                the challenges<br>
                of today's world
            </p>
        </div>
        <!-- 2/3 Right Section with Lists and Text Block -->
        <div class=\"col-md-8 col-sm-12 pt-5\">
            <div class=\"container text-dark text-start\">
                <div class=\"row\">
                    <!-- Problem Section -->
                    <div class=\"col\">
                        <div class=\"p-3\">
                            <h4 class=\"text-center fw-bold text-danger pb-3\">Problem</h4>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-x-circle-fill text-danger\"></i>
                                <span class=\"ms-2\">Profit-oriented progress</span>
                            </p>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-x-circle-fill text-danger\"></i>
                                <span class=\"ms-2\">Old state patents granted without any ethical control</span>
                            </p>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-x-circle-fill text-danger\"></i>
                                <span class=\"ms-2\">Increasing environmental destruction in the course of technological progress</span>
                            </p>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-x-circle-fill text-danger\"></i>
                                <span class=\"ms-2\">Hostility of public opinion towards change</span>
                            </p>
                        </div>
                    </div>
                    <!-- Solution Section -->
                    <div class=\"col\">
                        <div class=\"p-3\">
                            <h4 class=\"text-center fw-bold text-success pb-3\">Solution</h4>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-check-circle-fill text-success\"></i>
                                <span class=\"ms-2\">Genuine creators who are responsible for the innovations they introduce</span>
                            </p>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-check-circle-fill text-success\"></i>
                                <span class=\"ms-2\">Certification is reserved for CreaBOOKs that include a charter on ethical exploitation</span>
                            </p>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-check-circle-fill text-success\"></i>
                                <span class=\"ms-2\">The charter commits all stakeholders to continuous ecological improvement</span>
                            </p>
                            <p class=\"align-icon-text\">
                                <i class=\"bi bi-check-circle-fill text-success\"></i>
                                <span class=\"ms-2\">Public support for green startups</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
</div>
<hr>


<!-- Faq -->

<div class=\"container-fluid m-0 pb-3 text-light\">
<div class=\"row\">

<div class=\"col-md-4 col-sm-12 pt-5 fondbleu\">
<h1 class=\"fs-1 text-start text-light fw-bold \">
                <svg class=\"bi bi-square-fill\" xmlns=\"http://www.w3.org/2000/svg\" width=\"30\" height=\"30\" fill=\"#f29432\" viewBox=\"0 0 16 16\"><path d=\"M0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2z\"></path></svg>  FAQ
            </h1>
            <p class=\"text-light fs-4 text-start mx-3 p-4 pt-2 \">
                Frequently asked questions<br>
about the CreaFREE Ecosystem
            </p>
</div>

<div class=\"col pt-5\">
<div class=\"accordion accordion-flush\" id=\"accordionFlushExample\">
  <div class=\"accordion-item\">
    <h2 class=\"accordion-header\" id=\"flush-headingOne\">
      <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#flush-collapseOne\" aria-expanded=\"false\" aria-controls=\"flush-collapseOne\">
        What is a CreaBOOK?
      </button>
    </h2>
    <div id=\"flush-collapseOne\" class=\"accordion-collapse collapse\" aria-labelledby=\"flush-headingOne\" data-bs-parent=\"#accordionFlushExample\">
      <div class=\"accordion-body\">A CreaBOOK is a book that proves who the true author of the described and claimed creation is.</div>
    </div>
  </div>
  <div class=\"accordion-item\">
    <h2 class=\"accordion-header\" id=\"flush-headingTwo\">
      <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#flush-collapseTwo\" aria-expanded=\"false\" aria-controls=\"flush-collapseTwo\">
        Why must innovations be protected? 
      </button>
    </h2>
    <div id=\"flush-collapseTwo\" class=\"accordion-collapse collapse\" aria-labelledby=\"flush-headingTwo\" data-bs-parent=\"#accordionFlushExample\">
      <div class=\"accordion-body\">Innovations must be protected so that startups can finance their research and development and respond to the current global challenges.</div>
    </div>
  </div>
  <div class=\"accordion-item\">
    <h2 class=\"accordion-header\" id=\"flush-headingThree\">
      <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#flush-collapseThree\" aria-expanded=\"false\" aria-controls=\"flush-collapseThree\">
        How is a CreaBOOK protected in court?
      </button>
    </h2>
    <div id=\"flush-collapseThree\" class=\"accordion-collapse collapse\" aria-labelledby=\"flush-headingThree\" data-bs-parent=\"#accordionFlushExample\">
      <div class=\"accordion-body\">Protecting intellectual property in court requires complex, lengthy, risky and costly national litigation. The World creations’ Society (WcS) has developed a streamlined procedure called CreaPAX. It prepares and supports a simple, quick and cost-effective settlement based on good faith. In the event of a refusal by the alleged CreaBOOK infringer, the WcS-appointed expert advocates fast track national trials.</div>
    </div>
  </div>

<div class=\"accordion-item\">
    <h2 class=\"accordion-header\" id=\"flush-headingfourth\">
      <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#flush-collapsefourth\" aria-expanded=\"false\" aria-controls=\"flush-collapsefourth\">
        What are the main differences between Patents and CreaBOOKs? 
      </button>
    </h2>
    <div id=\"flush-collapsefourth\" class=\"accordion-collapse collapse\" aria-labelledby=\"flush-headingfourth\" data-bs-parent=\"#accordionFlushExample\">
      <div class=\"accordion-body\">Patents are “property” granted by governments to a small number of large corporations. CreaBOOKS are the recognized property rights of startups to their innovations.</div>
    </div>
  </div>
<div class=\"accordion-item\">
    <h2 class=\"accordion-header\" id=\"flush-headingfive\">
      <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#flush-collapsefive\" aria-expanded=\"false\" aria-controls=\"flush-collapsefive\">
        Can a CreaBOOK be updated?
      </button>
    </h2>
    <div id=\"flush-collapsefive\" class=\"accordion-collapse collapse\" aria-labelledby=\"flush-headingfive\" data-bs-parent=\"#accordionFlushExample\">
      <div class=\"accordion-body\">A CreaBOOK can be constantly revised and updated, depending on the improvements introduced into the innovation by the rights holders or through licenses agreed with other authors.</div>
    </div>
  </div>


<div class=\"accordion-item\">
    <h2 class=\"accordion-header\" id=\"flush-headingOne2\">
      <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#flush-collapseOne2\" aria-expanded=\"false\" aria-controls=\"flush-collapseOne\">
        What is the Universal Standard for Intellectual Property? 
      </button>
    </h2>
    <div id=\"flush-collapseOne2\" class=\"accordion-collapse collapse\" aria-labelledby=\"flush-headingOne2\" data-bs-parent=\"#accordionFlushExample2\">
      <div class=\"accordion-body\">The Universal Standard for Intellectual Property (USIP) is a standard developed by the WcS to implement the essential rights granted to authors by international treaties.</div>
    </div>
  </div>

</div>
</div>

<div class=\"col pt-5\">
<div class=\"accordion accordion-flush\" id=\"accordionFlushExample2\">
  
  <div class=\"accordion-item\">
    <h2 class=\"accordion-header\" id=\"flush-headingTwo2\">
      <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#flush-collapseTwo2\" aria-expanded=\"false\" aria-controls=\"flush-collapseTwo2\">
       What is the World creators Society (WcS) Organization?
      </button>
    </h2>
    <div id=\"flush-collapseTwo2\" class=\"accordion-collapse collapse\" aria-labelledby=\"flush-headingTwo2\" data-bs-parent=\"#accordionFlushExample2\">
      <div class=\"accordion-body\">WcS is a non-governmental organization (NGO) composed of creators that manages the CreaFREE Ecosystem to implement the USIP.</div>
    </div>
  </div>
  <div class=\"accordion-item\">
    <h2 class=\"accordion-header\" id=\"flush-headingThree2\">
      <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#flush-collapseThree2\" aria-expanded=\"false\" aria-controls=\"flush-collapseThree\">
       Who are the experts accredited for certification of Creabooks? 
      </button>
    </h2>
    <div id=\"flush-collapseThree2\" class=\"accordion-collapse collapse\" aria-labelledby=\"flush-headingThree2\" data-bs-parent=\"#accordionFlushExample2\">
      <div class=\"accordion-body\">The experts who certify CreaBOOKs are selected from national intellectual property consultants or, for sovereign certification, from worldwide specialists.</div>
    </div>
  </div>
<div class=\"accordion-item\">
    <h2 class=\"accordion-header\" id=\"flush-headingfourth2\">
      <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#flush-collapsefourth2\" aria-expanded=\"false\" aria-controls=\"flush-collapsefourth2\">
        Can pledges be placed on CreaBOOKs to support startups? 
      </button>
    </h2>
    <div id=\"flush-collapsefourth2\" class=\"accordion-collapse collapse\" aria-labelledby=\"flush-headingfourth2\" data-bs-parent=\"#accordionFlushExample2\">
      <div class=\"accordion-body\">Governments, local authorities, sponsors or partners of a startup can receive a pledge on CreaBOOK in return for active support.</div>
    </div>
  </div>
<div class=\"accordion-item\">
    <h2 class=\"accordion-header\" id=\"flush-headingfive2\">
      <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#flush-collapsefive2\" aria-expanded=\"false\" aria-controls=\"flush-collapsefive2\">
        What is the “multiplier 1000”? 
      </button>
    </h2>
    <div id=\"flush-collapsefive2\" class=\"accordion-collapse collapse\" aria-labelledby=\"flush-headingfive2\" data-bs-parent=\"#accordionFlushExample2\">
      <div class=\"accordion-body\">The “multiplier 1000” is a coefficient for measuring the potential of startups that are on their way to becoming unicorns.</div>
    </div>
  </div>

<div class=\"accordion-item\">
    <h2 class=\"accordion-header\" id=\"flush-headingsix2\">
      <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#flush-collapsesix2\" aria-expanded=\"false\" aria-controls=\"flush-collapsesix2\">
        How is ethical intellectual property implemented? 
      </button>
    </h2>
    <div id=\"flush-collapsesix2\" class=\"accordion-collapse collapse\" aria-labelledby=\"flush-headingsix2\" data-bs-parent=\"#accordionFlushExample2\">
      <div class=\"accordion-body\">TCreaBOOKs are published by the World creations Register without any examination or condition. However, only CreaBOOKs that include a charter for ethical exploitation can apply for certification by WcS. The provisions of this charter will be included in all contracts relating to the protected innovation. Any user of an authentic product or service resulting from an innovation protected by a CreaBOOK has the right to request the implementation of this charter.</div>
    </div>
  </div>



</div>
</div>

</div>
</div>
</div>
      ";
        // line 487
        if (twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_second", [], "any", false, false, true, 487)) {
            // line 488
            echo "        <div class=\"order-3 ";
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["sidebar_second_classes"] ?? null), 488, $this->source), "html", null, true);
            echo "\">
          ";
            // line 489
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_second", [], "any", false, false, true, 489), 489, $this->source), "html", null, true);
            echo "
        </div>
      ";
        }
        // line 492
        echo "    </div>
  </div>

</main>

";
        // line 497
        if (twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "footer", [], "any", false, false, true, 497)) {
            // line 498
            echo "<footer class=\"mt-auto ";
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["footer_classes"] ?? null), 498, $this->source), "html", null, true);
            echo "\">
  <div class=\"";
            // line 499
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["b5_top_container"] ?? null), 499, $this->source), "html", null, true);
            echo "\">
    ";
            // line 500
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["page"] ?? null), "footer", [], "any", false, false, true, 500), 500, $this->source), "html", null, true);
            echo "
  </div>
</footer>
";
        }
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["b5_navbar_schema", "b5_navbar_bg_schema", "b5_footer_schema", "b5_footer_bg_schema", "page", "b5_top_container", "logo"]);    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName()
    {
        return "themes/contrib/bootstrap5/templates/content/page--front.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable()
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo()
    {
        return array (  584 => 500,  580 => 499,  575 => 498,  573 => 497,  566 => 492,  560 => 489,  555 => 488,  553 => 487,  246 => 183,  241 => 182,  235 => 179,  230 => 178,  228 => 177,  225 => 176,  219 => 174,  217 => 173,  213 => 172,  209 => 170,  207 => 168,  204 => 166,  202 => 164,  199 => 162,  197 => 160,  194 => 158,  187 => 152,  182 => 149,  176 => 146,  172 => 145,  163 => 138,  161 => 137,  148 => 126,  140 => 123,  133 => 122,  130 => 121,  126 => 119,  121 => 118,  119 => 117,  114 => 115,  110 => 113,  108 => 111,  107 => 110,  106 => 109,  105 => 108,  102 => 106,  100 => 104,  99 => 103,  98 => 102,  97 => 101,  77 => 82,  42 => 48,  39 => 46,);
    }

    public function getSourceContext()
    {
        return new Source("", "themes/contrib/bootstrap5/templates/content/page--front.html.twig", "/var/www/html/web/themes/contrib/bootstrap5/templates/content/page--front.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = array("set" => 101, "if" => 117);
        static $filters = array("escape" => 115, "t" => 122);
        static $functions = array("path" => 122);

        try {
            $this->sandbox->checkSecurity(
                ['set', 'if'],
                ['escape', 't'],
                ['path']
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}
